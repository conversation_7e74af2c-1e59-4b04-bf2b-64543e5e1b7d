"""
Search widget for the EDMS application.

Provides advanced search functionality with filters, sorting,
and result display for document management.
"""

import logging
from datetime import datetime, date
from typing import Optional, List, Dict, Any

from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QDateEdit, QCheckBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox, QFrame,
    QSplitter, QTextEdit, QProgressBar, QSpinBox
)

from app.models.document import Document, DocumentStatus
from app.utils.ui_utils import show_error_dialog, format_file_size_for_display
from config import get_config

logger = logging.getLogger(__name__)

class SearchWidget(QWidget):
    """Advanced search widget for documents."""
    
    # Signals
    document_selected = pyqtSignal(int)  # document_id
    search_completed = pyqtSignal(int)   # result_count
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = get_config()
        self.current_results: List[Document] = []
        self.search_timer = QTimer()
        
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        
        # Create search form
        self.create_search_form(layout)
        
        # Create results section
        self.create_results_section(layout)
    
    def create_search_form(self, parent_layout: QVBoxLayout):
        """Create the search form section."""
        form_group = QGroupBox("Search Criteria")
        form_layout = QGridLayout(form_group)
        
        # Basic search
        form_layout.addWidget(QLabel("Search Text:"), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter keywords, title, or content...")
        form_layout.addWidget(self.search_input, 0, 1, 1, 2)
        
        # Search button
        self.search_button = QPushButton("Search")
        self.search_button.clicked.connect(self.perform_search)
        form_layout.addWidget(self.search_button, 0, 3)
        
        # Advanced filters
        self.create_advanced_filters(form_layout)
        
        # Search options
        self.create_search_options(form_layout)
        
        parent_layout.addWidget(form_group)
    
    def create_advanced_filters(self, form_layout: QGridLayout):
        """Create advanced filter controls."""
        row = 1
        
        # Status filter
        form_layout.addWidget(QLabel("Status:"), row, 0)
        self.status_filter = QComboBox()
        self.status_filter.addItem("Any Status", "")
        for status in DocumentStatus:
            self.status_filter.addItem(status.value.title(), status.value)
        form_layout.addWidget(self.status_filter, row, 1)
        
        # Organization filter
        form_layout.addWidget(QLabel("Organization:"), row, 2)
        self.organization_filter = QComboBox()
        self.organization_filter.addItem("Any Organization", "")
        self.load_organizations()
        form_layout.addWidget(self.organization_filter, row, 3)
        
        row += 1
        
        # Date range
        form_layout.addWidget(QLabel("Date From:"), row, 0)
        self.date_from = QDateEdit()
        self.date_from.setCalendarPopup(True)
        self.date_from.setSpecialValueText("Any Date")
        self.date_from.setDate(date.today().replace(year=date.today().year - 1))
        form_layout.addWidget(self.date_from, row, 1)
        
        form_layout.addWidget(QLabel("Date To:"), row, 2)
        self.date_to = QDateEdit()
        self.date_to.setCalendarPopup(True)
        self.date_to.setSpecialValueText("Any Date")
        self.date_to.setDate(date.today())
        form_layout.addWidget(self.date_to, row, 3)
        
        row += 1
        
        # File type filter
        form_layout.addWidget(QLabel("File Type:"), row, 0)
        self.file_type_filter = QComboBox()
        self.file_type_filter.addItems([
            "Any Type", "PDF", "Word", "Excel", "PowerPoint", "Image", "Text"
        ])
        form_layout.addWidget(self.file_type_filter, row, 1)
        
        # Owner filter
        form_layout.addWidget(QLabel("Owner:"), row, 2)
        self.owner_filter = QComboBox()
        self.owner_filter.addItem("Any Owner", "")
        self.load_owners()
        form_layout.addWidget(self.owner_filter, row, 3)
    
    def create_search_options(self, form_layout: QGridLayout):
        """Create search option controls."""
        row = 4
        
        # Search options
        options_layout = QHBoxLayout()
        
        self.fuzzy_search_cb = QCheckBox("Fuzzy Search")
        self.fuzzy_search_cb.setChecked(True)
        options_layout.addWidget(self.fuzzy_search_cb)
        
        self.content_search_cb = QCheckBox("Search in Content")
        self.content_search_cb.setChecked(True)
        options_layout.addWidget(self.content_search_cb)
        
        self.case_sensitive_cb = QCheckBox("Case Sensitive")
        options_layout.addWidget(self.case_sensitive_cb)
        
        options_layout.addStretch()
        
        # Results limit
        options_layout.addWidget(QLabel("Max Results:"))
        self.results_limit = QSpinBox()
        self.results_limit.setRange(10, 1000)
        self.results_limit.setValue(100)
        options_layout.addWidget(self.results_limit)
        
        form_layout.addLayout(options_layout, row, 0, 1, 4)
    
    def create_results_section(self, parent_layout: QVBoxLayout):
        """Create the search results section."""
        results_group = QGroupBox("Search Results")
        results_layout = QVBoxLayout(results_group)
        
        # Results header
        header_layout = QHBoxLayout()
        
        self.results_label = QLabel("No search performed")
        header_layout.addWidget(self.results_label)
        
        header_layout.addStretch()
        
        # Export button
        self.export_button = QPushButton("Export Results")
        self.export_button.setEnabled(False)
        self.export_button.clicked.connect(self.export_results)
        header_layout.addWidget(self.export_button)
        
        # Clear button
        self.clear_button = QPushButton("Clear")
        self.clear_button.clicked.connect(self.clear_search)
        header_layout.addWidget(self.clear_button)
        
        results_layout.addLayout(header_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        results_layout.addWidget(self.progress_bar)
        
        # Results table
        self.create_results_table(results_layout)
        
        parent_layout.addWidget(results_group)
    
    def create_results_table(self, parent_layout: QVBoxLayout):
        """Create the results table."""
        self.results_table = QTableWidget()
        
        # Set columns
        columns = ["Title", "Organization", "Status", "Owner", "Date", "Size", "Score"]
        self.results_table.setColumnCount(len(columns))
        self.results_table.setHorizontalHeaderLabels(columns)
        
        # Configure table
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSortingEnabled(True)
        
        # Set column widths
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Title
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Organization
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Owner
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Size
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Score
        
        # Connect selection signal
        self.results_table.itemSelectionChanged.connect(self.on_result_selected)
        
        parent_layout.addWidget(self.results_table)
    
    def setup_connections(self):
        """Setup signal connections."""
        # Auto-search with delay
        self.search_input.textChanged.connect(self.on_search_text_changed)
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_auto_search)
        
        # Filter changes
        self.status_filter.currentTextChanged.connect(self.on_filter_changed)
        self.organization_filter.currentTextChanged.connect(self.on_filter_changed)
        self.file_type_filter.currentTextChanged.connect(self.on_filter_changed)
        self.owner_filter.currentTextChanged.connect(self.on_filter_changed)
        
        # Enter key in search box
        self.search_input.returnPressed.connect(self.perform_search)
    
    def load_organizations(self):
        """Load organizations for filter dropdown."""
        try:
            # TODO: Load organizations from database
            organizations = ["HR", "Finance", "IT", "Legal", "Marketing"]
            for org in organizations:
                self.organization_filter.addItem(org, org)
        except Exception as e:
            logger.error(f"Error loading organizations: {e}")
    
    def load_owners(self):
        """Load owners for filter dropdown."""
        try:
            # TODO: Load users from database
            owners = ["John Doe", "Jane Smith", "Admin User"]
            for owner in owners:
                self.owner_filter.addItem(owner, owner)
        except Exception as e:
            logger.error(f"Error loading owners: {e}")
    
    def on_search_text_changed(self):
        """Handle search text changes with auto-search delay."""
        self.search_timer.stop()
        if self.search_input.text().strip():
            self.search_timer.start(500)  # 500ms delay
    
    def on_filter_changed(self):
        """Handle filter changes."""
        if self.current_results:
            self.perform_search()
    
    def perform_auto_search(self):
        """Perform automatic search after text change delay."""
        if self.search_input.text().strip():
            self.perform_search()
    
    def perform_search(self):
        """Perform the document search."""
        query = self.search_input.text().strip()
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
        self.results_label.setText("Searching...")
        
        try:
            # Build search filters
            filters = self.build_search_filters()
            
            # Perform search
            if query or any(filters.values()):
                results = Document.search(
                    query=query,
                    filters=filters,
                    limit=self.results_limit.value()
                )
            else:
                results = Document.get_all(limit=self.results_limit.value())
            
            # Update results
            self.current_results = results
            self.update_results_display()
            
            # Update status
            count = len(results)
            self.results_label.setText(f"Found {count} document(s)")
            self.export_button.setEnabled(count > 0)
            
            # Emit signal
            self.search_completed.emit(count)
            
        except Exception as e:
            logger.error(f"Search error: {e}")
            show_error_dialog(self, "Search Error", f"Search failed: {str(e)}")
            self.results_label.setText("Search failed")
        
        finally:
            self.progress_bar.setVisible(False)
    
    def build_search_filters(self) -> Dict[str, Any]:
        """Build search filters from form inputs."""
        filters = {}
        
        # Status filter
        status = self.status_filter.currentData()
        if status:
            filters['status'] = status
        
        # Organization filter
        organization = self.organization_filter.currentData()
        if organization:
            filters['organization'] = organization
        
        # Date range
        if self.date_from.date() != self.date_from.minimumDate():
            filters['date_from'] = self.date_from.date().toPyDate()
        
        if self.date_to.date() != self.date_to.maximumDate():
            filters['date_to'] = self.date_to.date().toPyDate()
        
        # File type filter
        file_type = self.file_type_filter.currentText()
        if file_type != "Any Type":
            filters['document_type'] = file_type.lower()
        
        # Owner filter
        owner = self.owner_filter.currentData()
        if owner:
            filters['owner'] = owner
        
        return filters
    
    def update_results_display(self):
        """Update the results table with search results."""
        self.results_table.setRowCount(len(self.current_results))
        
        for row, document in enumerate(self.current_results):
            # Title
            title_item = QTableWidgetItem(document.title)
            title_item.setData(Qt.UserRole, document.id)
            self.results_table.setItem(row, 0, title_item)
            
            # Organization
            org_item = QTableWidgetItem(document.organization or "")
            self.results_table.setItem(row, 1, org_item)
            
            # Status
            status_item = QTableWidgetItem(document.status.title())
            self.results_table.setItem(row, 2, status_item)
            
            # Owner
            owner_item = QTableWidgetItem(document.owner.full_name if document.owner else "")
            self.results_table.setItem(row, 3, owner_item)
            
            # Date
            date_str = document.document_date.strftime('%Y-%m-%d') if document.document_date else ""
            date_item = QTableWidgetItem(date_str)
            self.results_table.setItem(row, 4, date_item)
            
            # Size
            size_item = QTableWidgetItem(format_file_size_for_display(document.file_size))
            self.results_table.setItem(row, 5, size_item)
            
            # Score (placeholder)
            score_item = QTableWidgetItem("100%")
            self.results_table.setItem(row, 6, score_item)
    
    def on_result_selected(self):
        """Handle result selection."""
        selected_items = self.results_table.selectedItems()
        if selected_items:
            row = selected_items[0].row()
            title_item = self.results_table.item(row, 0)
            if title_item:
                document_id = title_item.data(Qt.UserRole)
                self.document_selected.emit(document_id)
    
    def clear_search(self):
        """Clear search form and results."""
        # Clear form
        self.search_input.clear()
        self.status_filter.setCurrentIndex(0)
        self.organization_filter.setCurrentIndex(0)
        self.file_type_filter.setCurrentIndex(0)
        self.owner_filter.setCurrentIndex(0)
        self.date_from.setDate(self.date_from.minimumDate())
        self.date_to.setDate(self.date_to.maximumDate())
        
        # Clear results
        self.current_results = []
        self.results_table.setRowCount(0)
        self.results_label.setText("No search performed")
        self.export_button.setEnabled(False)
    
    def export_results(self):
        """Export search results to CSV."""
        if not self.current_results:
            return
        
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            
            # Get save location
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Search Results",
                f"search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )
            
            if not file_path:
                return
            
            # Export to CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write header
                writer.writerow([
                    'Title', 'Organization', 'Status', 'Owner', 'Date', 
                    'Size', 'File Name', 'Keywords'
                ])
                
                # Write data
                for doc in self.current_results:
                    writer.writerow([
                        doc.title,
                        doc.organization or '',
                        doc.status.title(),
                        doc.owner.full_name if doc.owner else '',
                        doc.document_date.strftime('%Y-%m-%d') if doc.document_date else '',
                        format_file_size_for_display(doc.file_size),
                        doc.file_name,
                        ', '.join(doc.keywords) if doc.keywords else ''
                    ])
            
            self.results_label.setText(f"Results exported to {file_path}")
            
        except Exception as e:
            logger.error(f"Export error: {e}")
            show_error_dialog(self, "Export Error", f"Failed to export results: {str(e)}")
    
    def get_selected_document_id(self) -> Optional[int]:
        """Get the ID of the currently selected document."""
        selected_items = self.results_table.selectedItems()
        if selected_items:
            row = selected_items[0].row()
            title_item = self.results_table.item(row, 0)
            if title_item:
                return title_item.data(Qt.UserRole)
        return None
