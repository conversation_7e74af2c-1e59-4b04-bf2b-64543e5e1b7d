"""
OCR (Optical Character Recognition) service for the EDMS application.

Provides text extraction from images and PDF files using Tesseract OCR
with configurable languages and confidence thresholds.
"""

import logging
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any, List

from config import get_config

logger = logging.getLogger(__name__)

class OCRService:
    """Service for optical character recognition operations."""
    
    def __init__(self):
        self.config = get_config()
        self.ocr_config = self.config['ocr']
        self.enabled = self.ocr_config['enabled']
        
        # Initialize OCR engine if enabled
        if self.enabled:
            self._initialize_ocr()
    
    def _initialize_ocr(self):
        """Initialize OCR engine and dependencies."""
        try:
            import pytesseract
            from PIL import Image
            import cv2
            
            self.pytesseract = pytesseract
            self.Image = Image
            self.cv2 = cv2
            
            # Set tesseract path if specified
            if self.ocr_config['tesseract_path']:
                pytesseract.pytesseract.tesseract_cmd = self.ocr_config['tesseract_path']
            
            # Test OCR availability
            try:
                test_result = pytesseract.get_tesseract_version()
                logger.info(f"OCR initialized successfully. Tesseract version: {test_result}")
            except Exception as e:
                logger.warning(f"OCR test failed: {e}")
                self.enabled = False
                
        except ImportError as e:
            logger.warning(f"OCR dependencies not available: {e}")
            self.enabled = False
    
    def is_enabled(self) -> bool:
        """Check if OCR is enabled and available."""
        return self.enabled
    
    def extract_text_from_image(self, image_path: Path, 
                               languages: Optional[List[str]] = None,
                               confidence_threshold: Optional[int] = None) -> Dict[str, Any]:
        """
        Extract text from image file.
        
        Args:
            image_path: Path to image file
            languages: List of languages for OCR (default from config)
            confidence_threshold: Minimum confidence threshold (default from config)
            
        Returns:
            Dictionary with extraction results
        """
        result = {
            'success': False,
            'text': '',
            'confidence': 0,
            'word_count': 0,
            'language': None,
            'error': None
        }
        
        if not self.enabled:
            result['error'] = 'OCR is not enabled or available'
            return result
        
        if not image_path.exists():
            result['error'] = 'Image file does not exist'
            return result
        
        try:
            # Use default values from config if not provided
            if languages is None:
                languages = self.ocr_config['languages']
            if confidence_threshold is None:
                confidence_threshold = self.ocr_config['confidence_threshold']
            
            # Load and preprocess image
            image = self._preprocess_image(image_path)
            
            # Perform OCR
            lang_string = '+'.join(languages)
            
            # Extract text with confidence data
            ocr_data = self.pytesseract.image_to_data(
                image, 
                lang=lang_string,
                output_type=self.pytesseract.Output.DICT
            )
            
            # Filter by confidence and extract text
            filtered_text = []
            total_confidence = 0
            word_count = 0
            
            for i, confidence in enumerate(ocr_data['conf']):
                if int(confidence) >= confidence_threshold:
                    text = ocr_data['text'][i].strip()
                    if text:
                        filtered_text.append(text)
                        total_confidence += int(confidence)
                        word_count += 1
            
            # Calculate average confidence
            avg_confidence = total_confidence / word_count if word_count > 0 else 0
            
            # Join text
            extracted_text = ' '.join(filtered_text)
            
            result.update({
                'success': True,
                'text': extracted_text,
                'confidence': round(avg_confidence, 2),
                'word_count': word_count,
                'language': lang_string
            })
            
            logger.info(f"OCR completed: {word_count} words extracted with {avg_confidence:.1f}% confidence")
            
        except Exception as e:
            result['error'] = f'OCR extraction failed: {str(e)}'
            logger.error(f"OCR error for {image_path}: {e}")
        
        return result
    
    def _preprocess_image(self, image_path: Path):
        """
        Preprocess image for better OCR results.
        
        Args:
            image_path: Path to image file
            
        Returns:
            Preprocessed image
        """
        try:
            # Load image with OpenCV
            image = self.cv2.imread(str(image_path))
            
            # Convert to grayscale
            gray = self.cv2.cvtColor(image, self.cv2.COLOR_BGR2GRAY)
            
            # Apply noise reduction
            denoised = self.cv2.medianBlur(gray, 3)
            
            # Apply adaptive thresholding
            thresh = self.cv2.adaptiveThreshold(
                denoised, 255, self.cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                self.cv2.THRESH_BINARY, 11, 2
            )
            
            # Convert back to PIL Image
            pil_image = self.Image.fromarray(thresh)
            
            return pil_image
            
        except Exception as e:
            logger.warning(f"Image preprocessing failed, using original: {e}")
            # Fallback to original image
            return self.Image.open(image_path)
    
    def extract_text_from_pdf(self, pdf_path: Path, 
                             max_pages: Optional[int] = None) -> Dict[str, Any]:
        """
        Extract text from PDF file (OCR for image-based PDFs).
        
        Args:
            pdf_path: Path to PDF file
            max_pages: Maximum number of pages to process
            
        Returns:
            Dictionary with extraction results
        """
        result = {
            'success': False,
            'text': '',
            'pages_processed': 0,
            'total_pages': 0,
            'confidence': 0,
            'error': None
        }
        
        if not self.enabled:
            result['error'] = 'OCR is not enabled or available'
            return result
        
        try:
            import pdf2image
            
            # Convert PDF pages to images
            pages = pdf2image.convert_from_path(pdf_path)
            result['total_pages'] = len(pages)
            
            if max_pages:
                pages = pages[:max_pages]
            
            all_text = []
            total_confidence = 0
            pages_with_text = 0
            
            # Process each page
            for i, page in enumerate(pages):
                try:
                    # Save page as temporary image
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                        page.save(temp_file.name, 'PNG')
                        temp_path = Path(temp_file.name)
                    
                    # Extract text from page
                    page_result = self.extract_text_from_image(temp_path)
                    
                    # Clean up temporary file
                    temp_path.unlink()
                    
                    if page_result['success'] and page_result['text'].strip():
                        all_text.append(f"--- Page {i+1} ---")
                        all_text.append(page_result['text'])
                        total_confidence += page_result['confidence']
                        pages_with_text += 1
                    
                    result['pages_processed'] += 1
                    
                except Exception as e:
                    logger.warning(f"Error processing PDF page {i+1}: {e}")
                    continue
            
            # Calculate results
            if pages_with_text > 0:
                result.update({
                    'success': True,
                    'text': '\n\n'.join(all_text),
                    'confidence': round(total_confidence / pages_with_text, 2)
                })
            else:
                result['error'] = 'No text could be extracted from PDF'
            
            logger.info(f"PDF OCR completed: {result['pages_processed']} pages processed")
            
        except ImportError:
            result['error'] = 'pdf2image library not available for PDF OCR'
        except Exception as e:
            result['error'] = f'PDF OCR failed: {str(e)}'
            logger.error(f"PDF OCR error for {pdf_path}: {e}")
        
        return result
    
    def extract_text_from_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text from file (auto-detect type).
        
        Args:
            file_path: Path to file
            
        Returns:
            Dictionary with extraction results
        """
        if not file_path.exists():
            return {
                'success': False,
                'text': '',
                'error': 'File does not exist'
            }
        
        # Determine file type
        file_extension = file_path.suffix.lower()
        
        if file_extension == '.pdf':
            return self.extract_text_from_pdf(file_path)
        elif file_extension in ['.jpg', '.jpeg', '.png', '.tiff', '.bmp', '.gif']:
            return self.extract_text_from_image(file_path)
        else:
            return {
                'success': False,
                'text': '',
                'error': f'Unsupported file type for OCR: {file_extension}'
            }
    
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported OCR languages.
        
        Returns:
            List of language codes
        """
        if not self.enabled:
            return []
        
        try:
            languages = self.pytesseract.get_languages()
            return sorted(languages)
        except Exception as e:
            logger.error(f"Error getting OCR languages: {e}")
            return self.ocr_config['languages']
    
    def test_ocr(self) -> Dict[str, Any]:
        """
        Test OCR functionality.
        
        Returns:
            Dictionary with test results
        """
        result = {
            'enabled': self.enabled,
            'tesseract_available': False,
            'version': None,
            'languages': [],
            'test_extraction': False,
            'error': None
        }
        
        if not self.enabled:
            result['error'] = 'OCR is disabled in configuration'
            return result
        
        try:
            # Test Tesseract availability
            version = self.pytesseract.get_tesseract_version()
            result['tesseract_available'] = True
            result['version'] = str(version)
            
            # Get available languages
            result['languages'] = self.get_supported_languages()
            
            # Test text extraction with a simple image
            try:
                # Create a simple test image with text
                import numpy as np
                
                # Create white image with black text
                img = np.ones((100, 300, 3), dtype=np.uint8) * 255
                
                # Convert to PIL and add text (simplified test)
                pil_img = self.Image.fromarray(img)
                
                # Simple OCR test
                test_text = self.pytesseract.image_to_string(pil_img)
                result['test_extraction'] = True
                
            except Exception as e:
                result['error'] = f'OCR test extraction failed: {str(e)}'
            
        except Exception as e:
            result['error'] = f'OCR test failed: {str(e)}'
        
        return result

# Global OCR service instance
ocr_service = OCRService()
