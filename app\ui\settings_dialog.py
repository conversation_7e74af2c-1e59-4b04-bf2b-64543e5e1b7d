"""
Settings dialog for the EDMS application.

Provides interface for configuring application settings including
UI preferences, storage options, security settings, and OCR configuration.
"""

import logging
from typing import Dict, Any

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QPushButton, QLineEdit, QSpinBox, QCheckBox, QComboBox,
    QGroupBox, QFormLayout, QFileDialog, QMessageBox, QSlider,
    QTextEdit, QDialogButtonBox
)

from app.services.ocr_service import ocr_service
from app.utils.ui_utils import show_message_box, show_error_dialog
from config import get_config

logger = logging.getLogger(__name__)

class SettingsDialog(QDialog):
    """Settings configuration dialog."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = get_config()
        self.modified_settings = {}
        
        self.init_ui()
        self.load_current_settings()
    
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("Settings")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Add tabs
        self.create_general_tab()
        self.create_storage_tab()
        self.create_security_tab()
        self.create_ocr_tab()
        self.create_ui_tab()
        
        layout.addWidget(self.tab_widget)
        
        # Create buttons
        self.create_buttons(layout)
    
    def create_general_tab(self):
        """Create general settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Application settings
        app_group = QGroupBox("Application Settings")
        app_layout = QFormLayout(app_group)
        
        self.auto_save_interval = QSpinBox()
        self.auto_save_interval.setRange(60, 3600)
        self.auto_save_interval.setSuffix(" seconds")
        app_layout.addRow("Auto-save interval:", self.auto_save_interval)
        
        self.items_per_page = QSpinBox()
        self.items_per_page.setRange(10, 500)
        app_layout.addRow("Items per page:", self.items_per_page)
        
        self.enable_notifications = QCheckBox("Enable notifications")
        app_layout.addRow("Notifications:", self.enable_notifications)
        
        layout.addWidget(app_group)
        
        # Search settings
        search_group = QGroupBox("Search Settings")
        search_layout = QFormLayout(search_group)
        
        self.max_search_results = QSpinBox()
        self.max_search_results.setRange(10, 10000)
        search_layout.addRow("Max search results:", self.max_search_results)
        
        self.search_timeout = QSpinBox()
        self.search_timeout.setRange(5, 300)
        self.search_timeout.setSuffix(" seconds")
        search_layout.addRow("Search timeout:", self.search_timeout)
        
        self.enable_fuzzy_search = QCheckBox("Enable fuzzy search")
        search_layout.addRow("Fuzzy search:", self.enable_fuzzy_search)
        
        self.highlight_matches = QCheckBox("Highlight search matches")
        search_layout.addRow("Highlight matches:", self.highlight_matches)
        
        layout.addWidget(search_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "General")
    
    def create_storage_tab(self):
        """Create storage settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Storage paths
        paths_group = QGroupBox("Storage Paths")
        paths_layout = QFormLayout(paths_group)
        
        # Documents path
        doc_path_layout = QHBoxLayout()
        self.documents_path = QLineEdit()
        self.documents_path.setReadOnly(True)
        doc_browse_btn = QPushButton("Browse...")
        doc_browse_btn.clicked.connect(lambda: self.browse_directory(self.documents_path))
        doc_path_layout.addWidget(self.documents_path)
        doc_path_layout.addWidget(doc_browse_btn)
        paths_layout.addRow("Documents:", doc_path_layout)
        
        # Backups path
        backup_path_layout = QHBoxLayout()
        self.backups_path = QLineEdit()
        self.backups_path.setReadOnly(True)
        backup_browse_btn = QPushButton("Browse...")
        backup_browse_btn.clicked.connect(lambda: self.browse_directory(self.backups_path))
        backup_path_layout.addWidget(self.backups_path)
        backup_path_layout.addWidget(backup_browse_btn)
        paths_layout.addRow("Backups:", backup_path_layout)
        
        layout.addWidget(paths_group)
        
        # File settings
        file_group = QGroupBox("File Settings")
        file_layout = QFormLayout(file_group)
        
        self.max_file_size = QSpinBox()
        self.max_file_size.setRange(1, 1000)
        self.max_file_size.setSuffix(" MB")
        file_layout.addRow("Max file size:", self.max_file_size)
        
        self.thumbnail_size = QSpinBox()
        self.thumbnail_size.setRange(50, 500)
        self.thumbnail_size.setSuffix(" px")
        file_layout.addRow("Thumbnail size:", self.thumbnail_size)
        
        layout.addWidget(file_group)
        
        # Backup settings
        backup_group = QGroupBox("Backup Settings")
        backup_layout = QFormLayout(backup_group)
        
        self.auto_backup = QCheckBox("Enable automatic backups")
        backup_layout.addRow("Auto backup:", self.auto_backup)
        
        self.backup_interval = QSpinBox()
        self.backup_interval.setRange(1, 30)
        self.backup_interval.setSuffix(" days")
        backup_layout.addRow("Backup interval:", self.backup_interval)
        
        self.max_backups = QSpinBox()
        self.max_backups.setRange(1, 100)
        backup_layout.addRow("Max backups to keep:", self.max_backups)
        
        self.compress_backups = QCheckBox("Compress backups")
        backup_layout.addRow("Compression:", self.compress_backups)
        
        layout.addWidget(backup_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Storage")
    
    def create_security_tab(self):
        """Create security settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Password policy
        password_group = QGroupBox("Password Policy")
        password_layout = QFormLayout(password_group)
        
        self.min_password_length = QSpinBox()
        self.min_password_length.setRange(4, 50)
        password_layout.addRow("Minimum length:", self.min_password_length)
        
        self.require_numbers = QCheckBox("Require numbers")
        password_layout.addRow("Numbers:", self.require_numbers)
        
        self.require_special = QCheckBox("Require special characters")
        password_layout.addRow("Special chars:", self.require_special)
        
        layout.addWidget(password_group)
        
        # Session settings
        session_group = QGroupBox("Session Settings")
        session_layout = QFormLayout(session_group)
        
        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(5, 1440)
        self.session_timeout.setSuffix(" minutes")
        session_layout.addRow("Session timeout:", self.session_timeout)
        
        self.max_login_attempts = QSpinBox()
        self.max_login_attempts.setRange(3, 20)
        session_layout.addRow("Max login attempts:", self.max_login_attempts)
        
        self.lockout_duration = QSpinBox()
        self.lockout_duration.setRange(5, 1440)
        self.lockout_duration.setSuffix(" minutes")
        session_layout.addRow("Lockout duration:", self.lockout_duration)
        
        layout.addWidget(session_group)
        
        # Audit settings
        audit_group = QGroupBox("Audit Settings")
        audit_layout = QFormLayout(audit_group)
        
        self.enable_audit = QCheckBox("Enable audit logging")
        audit_layout.addRow("Audit logging:", self.enable_audit)
        
        self.audit_level = QComboBox()
        self.audit_level.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        audit_layout.addRow("Audit level:", self.audit_level)
        
        self.max_log_size = QSpinBox()
        self.max_log_size.setRange(1, 100)
        self.max_log_size.setSuffix(" MB")
        audit_layout.addRow("Max log size:", self.max_log_size)
        
        layout.addWidget(audit_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Security")
    
    def create_ocr_tab(self):
        """Create OCR settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # OCR settings
        ocr_group = QGroupBox("OCR Configuration")
        ocr_layout = QFormLayout(ocr_group)
        
        self.enable_ocr = QCheckBox("Enable OCR")
        ocr_layout.addRow("OCR:", self.enable_ocr)
        
        # Tesseract path
        tesseract_layout = QHBoxLayout()
        self.tesseract_path = QLineEdit()
        tesseract_browse_btn = QPushButton("Browse...")
        tesseract_browse_btn.clicked.connect(lambda: self.browse_file(self.tesseract_path, "Tesseract Executable (tesseract.exe)"))
        tesseract_layout.addWidget(self.tesseract_path)
        tesseract_layout.addWidget(tesseract_browse_btn)
        ocr_layout.addRow("Tesseract path:", tesseract_layout)
        
        # OCR languages
        self.ocr_languages = QLineEdit()
        self.ocr_languages.setPlaceholderText("eng,fra,deu (comma-separated)")
        ocr_layout.addRow("Languages:", self.ocr_languages)
        
        # Confidence threshold
        confidence_layout = QHBoxLayout()
        self.confidence_threshold = QSlider(Qt.Horizontal)
        self.confidence_threshold.setRange(0, 100)
        self.confidence_label = QLabel("60%")
        self.confidence_threshold.valueChanged.connect(
            lambda v: self.confidence_label.setText(f"{v}%")
        )
        confidence_layout.addWidget(self.confidence_threshold)
        confidence_layout.addWidget(self.confidence_label)
        ocr_layout.addRow("Confidence threshold:", confidence_layout)
        
        layout.addWidget(ocr_group)
        
        # OCR test
        test_group = QGroupBox("OCR Test")
        test_layout = QVBoxLayout(test_group)
        
        test_btn = QPushButton("Test OCR Configuration")
        test_btn.clicked.connect(self.test_ocr)
        test_layout.addWidget(test_btn)
        
        self.ocr_test_result = QTextEdit()
        self.ocr_test_result.setMaximumHeight(100)
        self.ocr_test_result.setReadOnly(True)
        test_layout.addWidget(self.ocr_test_result)
        
        layout.addWidget(test_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "OCR")
    
    def create_ui_tab(self):
        """Create UI settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Theme settings
        theme_group = QGroupBox("Theme Settings")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Default", "Dark", "Blue"])
        theme_layout.addRow("Theme:", self.theme_combo)
        
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 20)
        self.font_size.setSuffix(" pt")
        theme_layout.addRow("Font size:", self.font_size)
        
        layout.addWidget(theme_group)
        
        # Window settings
        window_group = QGroupBox("Window Settings")
        window_layout = QFormLayout(window_group)
        
        self.window_width = QSpinBox()
        self.window_width.setRange(800, 2000)
        window_layout.addRow("Default width:", self.window_width)
        
        self.window_height = QSpinBox()
        self.window_height.setRange(600, 1500)
        window_layout.addRow("Default height:", self.window_height)
        
        self.remember_window_state = QCheckBox("Remember window state")
        window_layout.addRow("Window state:", self.remember_window_state)
        
        layout.addWidget(window_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "UI")
    
    def create_buttons(self, parent_layout: QVBoxLayout):
        """Create dialog buttons."""
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        
        button_box.accepted.connect(self.accept_settings)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self.apply_settings)
        
        parent_layout.addWidget(button_box)
    
    def load_current_settings(self):
        """Load current settings into form controls."""
        # General settings
        self.auto_save_interval.setValue(self.config['ui']['auto_save_interval'])
        self.items_per_page.setValue(self.config['ui']['items_per_page'])
        
        # Search settings
        self.max_search_results.setValue(self.config['search']['max_results'])
        self.search_timeout.setValue(self.config['search']['search_timeout_seconds'])
        self.enable_fuzzy_search.setChecked(self.config['search']['fuzzy_search'])
        self.highlight_matches.setChecked(self.config['search']['highlight_matches'])
        
        # Storage settings
        self.documents_path.setText(str(self.config['storage']['documents_path']))
        self.backups_path.setText(str(self.config['storage']['backups_path']))
        self.max_file_size.setValue(self.config['storage']['max_file_size_mb'])
        self.thumbnail_size.setValue(self.config['ui']['thumbnail_size'][0])
        
        # Backup settings
        self.auto_backup.setChecked(self.config['backup']['auto_backup'])
        self.backup_interval.setValue(self.config['backup']['backup_interval_days'])
        self.max_backups.setValue(self.config['backup']['max_backups'])
        self.compress_backups.setChecked(self.config['backup']['compress_backups'])
        
        # Security settings
        self.min_password_length.setValue(self.config['security']['password_min_length'])
        self.require_numbers.setChecked(self.config['security']['password_require_numbers'])
        self.require_special.setChecked(self.config['security']['password_require_special'])
        self.session_timeout.setValue(self.config['security']['session_timeout_minutes'])
        self.max_login_attempts.setValue(self.config['security']['max_login_attempts'])
        self.lockout_duration.setValue(self.config['security']['lockout_duration_minutes'])
        
        # Audit settings
        self.enable_audit.setChecked(self.config['audit']['enabled'])
        self.audit_level.setCurrentText(self.config['audit']['log_level'])
        self.max_log_size.setValue(self.config['audit']['max_log_size_mb'])
        
        # OCR settings
        self.enable_ocr.setChecked(self.config['ocr']['enabled'])
        self.tesseract_path.setText(self.config['ocr']['tesseract_path'] or "")
        self.ocr_languages.setText(','.join(self.config['ocr']['languages']))
        self.confidence_threshold.setValue(self.config['ocr']['confidence_threshold'])
        
        # UI settings
        self.theme_combo.setCurrentText(self.config['ui']['theme'].title())
        self.window_width.setValue(self.config['ui']['window_size'][0])
        self.window_height.setValue(self.config['ui']['window_size'][1])
    
    def browse_directory(self, line_edit: QLineEdit):
        """Browse for directory."""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Directory", line_edit.text()
        )
        if directory:
            line_edit.setText(directory)
    
    def browse_file(self, line_edit: QLineEdit, file_filter: str):
        """Browse for file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select File", line_edit.text(), file_filter
        )
        if file_path:
            line_edit.setText(file_path)
    
    def test_ocr(self):
        """Test OCR configuration."""
        self.ocr_test_result.clear()
        self.ocr_test_result.append("Testing OCR configuration...")
        
        try:
            # Test OCR service
            test_result = ocr_service.test_ocr()
            
            if test_result.get('tesseract_available'):
                self.ocr_test_result.append(f"✓ Tesseract version: {test_result.get('version', 'Unknown')}")
                self.ocr_test_result.append(f"✓ Available languages: {', '.join(test_result.get('languages', []))}")
                
                if test_result.get('test_extraction'):
                    self.ocr_test_result.append("✓ Text extraction test passed")
                else:
                    self.ocr_test_result.append("⚠ Text extraction test failed")
            else:
                self.ocr_test_result.append("✗ Tesseract not available")
                if test_result.get('error'):
                    self.ocr_test_result.append(f"Error: {test_result['error']}")
        
        except Exception as e:
            self.ocr_test_result.append(f"✗ OCR test failed: {str(e)}")
    
    def apply_settings(self):
        """Apply settings without closing dialog."""
        # TODO: Implement settings application
        show_message_box(self, "Settings", "Settings applied successfully!")
    
    def accept_settings(self):
        """Accept and apply settings."""
        self.apply_settings()
        self.accept()
    
    def collect_settings(self) -> Dict[str, Any]:
        """Collect settings from form controls."""
        settings = {}
        
        # TODO: Collect all settings from form controls
        # This would be implemented to gather all the form values
        # and return them as a dictionary
        
        return settings
