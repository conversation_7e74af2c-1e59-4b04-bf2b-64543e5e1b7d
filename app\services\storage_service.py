"""
File storage service for the EDMS application.

Handles file upload, storage, retrieval, and management operations
including path generation, file validation, and cleanup.
"""

import hashlib
import logging
import mimetypes
import os
import shutil
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple, Dict, Any, List

from PIL import Image
from config import get_config

logger = logging.getLogger(__name__)

class StorageService:
    """Service for managing file storage operations."""
    
    def __init__(self):
        self.config = get_config()
        self.storage_config = self.config['storage']
        self.documents_path = Path(self.storage_config['documents_path'])
        self.thumbnails_path = Path(self.storage_config['thumbnails_path'])
        self.temp_path = Path(self.storage_config['temp_path'])
        self.backups_path = Path(self.storage_config['backups_path'])
        
        # Ensure directories exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure all storage directories exist."""
        directories = [
            self.documents_path,
            self.thumbnails_path,
            self.temp_path,
            self.backups_path
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Validate uploaded file against configuration rules.
        
        Args:
            file_path: Path to file to validate
            
        Returns:
            Dictionary with validation results
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        try:
            if not file_path.exists():
                result['valid'] = False
                result['errors'].append('File does not exist')
                return result
            
            # Check file size
            file_size = file_path.stat().st_size
            max_size_bytes = self.storage_config['max_file_size_mb'] * 1024 * 1024
            
            if file_size > max_size_bytes:
                result['valid'] = False
                result['errors'].append(f'File size ({file_size / 1024 / 1024:.1f} MB) exceeds maximum allowed size ({self.storage_config["max_file_size_mb"]} MB)')
            
            # Check file extension
            file_extension = file_path.suffix.lower()
            allowed_extensions = self.storage_config['allowed_extensions']
            
            if file_extension not in allowed_extensions:
                result['valid'] = False
                result['errors'].append(f'File extension "{file_extension}" is not allowed')
            
            # Get MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if not mime_type:
                mime_type = 'application/octet-stream'
                result['warnings'].append('Could not determine MIME type')
            
            # Calculate file hash
            file_hash = self.calculate_file_hash(file_path)
            
            # Store file information
            result['file_info'] = {
                'size': file_size,
                'extension': file_extension,
                'mime_type': mime_type,
                'hash': file_hash
            }
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f'File validation error: {str(e)}')
            logger.error(f'File validation error for {file_path}: {e}')
        
        return result
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """
        Calculate SHA-256 hash of file content.
        
        Args:
            file_path: Path to file
            
        Returns:
            Hexadecimal hash string
        """
        hash_sha256 = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        
        return hash_sha256.hexdigest()
    
    def generate_storage_path(self, original_filename: str, file_hash: str) -> str:
        """
        Generate storage path for a file.
        
        Args:
            original_filename: Original filename
            file_hash: File hash for uniqueness
            
        Returns:
            Relative storage path
        """
        # Use current date for directory structure
        now = datetime.now()
        year = now.strftime('%Y')
        month = now.strftime('%m')
        day = now.strftime('%d')
        
        # Generate unique filename using hash and timestamp
        file_extension = Path(original_filename).suffix.lower()
        unique_filename = f"{file_hash[:16]}_{int(now.timestamp())}{file_extension}"
        
        # Create relative path
        relative_path = Path(year) / month / day / unique_filename
        
        return str(relative_path)
    
    def store_file(self, source_path: Path, original_filename: str) -> Dict[str, Any]:
        """
        Store a file in the document storage.
        
        Args:
            source_path: Path to source file
            original_filename: Original filename
            
        Returns:
            Dictionary with storage results
        """
        result = {
            'success': False,
            'file_path': None,
            'file_size': 0,
            'file_hash': None,
            'mime_type': None,
            'error': None
        }
        
        try:
            # Validate file
            validation = self.validate_file(source_path)
            if not validation['valid']:
                result['error'] = '; '.join(validation['errors'])
                return result
            
            file_info = validation['file_info']
            file_hash = file_info['hash']
            
            # Generate storage path
            relative_path = self.generate_storage_path(original_filename, file_hash)
            absolute_path = self.documents_path / relative_path
            
            # Ensure target directory exists
            absolute_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file to storage
            shutil.copy2(source_path, absolute_path)
            
            # Verify copy
            if not absolute_path.exists():
                result['error'] = 'File copy failed'
                return result
            
            # Verify hash matches
            stored_hash = self.calculate_file_hash(absolute_path)
            if stored_hash != file_hash:
                absolute_path.unlink()  # Remove corrupted file
                result['error'] = 'File corruption detected during storage'
                return result
            
            result.update({
                'success': True,
                'file_path': relative_path,
                'file_size': file_info['size'],
                'file_hash': file_hash,
                'mime_type': file_info['mime_type']
            })
            
            logger.info(f'File stored successfully: {relative_path}')
            
        except Exception as e:
            result['error'] = f'Storage error: {str(e)}'
            logger.error(f'File storage error: {e}')
        
        return result
    
    def get_file_path(self, relative_path: str) -> Path:
        """
        Get absolute path to stored file.
        
        Args:
            relative_path: Relative path from storage root
            
        Returns:
            Absolute path to file
        """
        return self.documents_path / relative_path
    
    def file_exists(self, relative_path: str) -> bool:
        """
        Check if stored file exists.
        
        Args:
            relative_path: Relative path from storage root
            
        Returns:
            True if file exists, False otherwise
        """
        return self.get_file_path(relative_path).exists()
    
    def delete_file(self, relative_path: str) -> bool:
        """
        Delete stored file.
        
        Args:
            relative_path: Relative path from storage root
            
        Returns:
            True if successful, False otherwise
        """
        try:
            file_path = self.get_file_path(relative_path)
            if file_path.exists():
                file_path.unlink()
                logger.info(f'File deleted: {relative_path}')
                return True
            else:
                logger.warning(f'File not found for deletion: {relative_path}')
                return False
        except Exception as e:
            logger.error(f'File deletion error for {relative_path}: {e}')
            return False
    
    def create_thumbnail(self, file_path: Path, max_size: Tuple[int, int] = (150, 150)) -> Optional[str]:
        """
        Create thumbnail for image files.
        
        Args:
            file_path: Path to source file
            max_size: Maximum thumbnail dimensions
            
        Returns:
            Relative path to thumbnail or None if failed
        """
        try:
            # Check if file is an image
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if not mime_type or not mime_type.startswith('image/'):
                return None
            
            # Generate thumbnail path
            file_hash = self.calculate_file_hash(file_path)
            thumbnail_filename = f"{file_hash[:16]}_thumb.jpg"
            thumbnail_path = self.thumbnails_path / thumbnail_filename
            
            # Create thumbnail
            with Image.open(file_path) as image:
                # Convert to RGB if necessary
                if image.mode in ('RGBA', 'LA', 'P'):
                    image = image.convert('RGB')
                
                # Create thumbnail
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                image.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
            
            logger.info(f'Thumbnail created: {thumbnail_filename}')
            return thumbnail_filename
            
        except Exception as e:
            logger.error(f'Thumbnail creation error for {file_path}: {e}')
            return None
    
    def get_thumbnail_path(self, thumbnail_filename: str) -> Path:
        """Get absolute path to thumbnail."""
        return self.thumbnails_path / thumbnail_filename
    
    def cleanup_orphaned_files(self) -> Dict[str, int]:
        """
        Clean up files that are not referenced in the database.
        
        Returns:
            Dictionary with cleanup statistics
        """
        from app.models.document import Document
        
        stats = {
            'documents_checked': 0,
            'documents_deleted': 0,
            'thumbnails_checked': 0,
            'thumbnails_deleted': 0,
            'errors': 0
        }
        
        try:
            # Get all document file paths from database
            documents = Document.get_all(include_deleted=False)
            db_file_paths = set()
            db_thumbnail_paths = set()
            
            for doc in documents:
                if doc.file_path:
                    db_file_paths.add(doc.file_path)
                if doc.thumbnail_path:
                    db_thumbnail_paths.add(doc.thumbnail_path)
            
            # Check document files
            for file_path in self.documents_path.rglob('*'):
                if file_path.is_file():
                    stats['documents_checked'] += 1
                    relative_path = str(file_path.relative_to(self.documents_path))
                    
                    if relative_path not in db_file_paths:
                        try:
                            file_path.unlink()
                            stats['documents_deleted'] += 1
                            logger.info(f'Deleted orphaned document: {relative_path}')
                        except Exception as e:
                            stats['errors'] += 1
                            logger.error(f'Error deleting orphaned document {relative_path}: {e}')
            
            # Check thumbnail files
            for thumbnail_path in self.thumbnails_path.rglob('*'):
                if thumbnail_path.is_file():
                    stats['thumbnails_checked'] += 1
                    thumbnail_filename = thumbnail_path.name
                    
                    if thumbnail_filename not in db_thumbnail_paths:
                        try:
                            thumbnail_path.unlink()
                            stats['thumbnails_deleted'] += 1
                            logger.info(f'Deleted orphaned thumbnail: {thumbnail_filename}')
                        except Exception as e:
                            stats['errors'] += 1
                            logger.error(f'Error deleting orphaned thumbnail {thumbnail_filename}: {e}')
            
        except Exception as e:
            stats['errors'] += 1
            logger.error(f'Cleanup error: {e}')
        
        return stats
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """
        Get storage usage statistics.
        
        Returns:
            Dictionary with storage statistics
        """
        stats = {
            'documents': {'count': 0, 'size_bytes': 0},
            'thumbnails': {'count': 0, 'size_bytes': 0},
            'temp': {'count': 0, 'size_bytes': 0},
            'total_size_bytes': 0,
            'total_size_mb': 0
        }
        
        try:
            # Count documents
            for file_path in self.documents_path.rglob('*'):
                if file_path.is_file():
                    stats['documents']['count'] += 1
                    size = file_path.stat().st_size
                    stats['documents']['size_bytes'] += size
            
            # Count thumbnails
            for file_path in self.thumbnails_path.rglob('*'):
                if file_path.is_file():
                    stats['thumbnails']['count'] += 1
                    size = file_path.stat().st_size
                    stats['thumbnails']['size_bytes'] += size
            
            # Count temp files
            for file_path in self.temp_path.rglob('*'):
                if file_path.is_file():
                    stats['temp']['count'] += 1
                    size = file_path.stat().st_size
                    stats['temp']['size_bytes'] += size
            
            # Calculate totals
            stats['total_size_bytes'] = (
                stats['documents']['size_bytes'] +
                stats['thumbnails']['size_bytes'] +
                stats['temp']['size_bytes']
            )
            stats['total_size_mb'] = round(stats['total_size_bytes'] / (1024 * 1024), 2)
            
        except Exception as e:
            logger.error(f'Error calculating storage statistics: {e}')
        
        return stats
