/* Default theme stylesheet for EDMS application */

/* Main application styling */
QMainWindow {
    background-color: #f5f5f5;
    color: #333333;
}

/* Menu bar styling */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    padding: 4px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QMenuBar::item:pressed {
    background-color: #bbdefb;
}

/* Menu styling */
QMenu {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 24px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QMenu::separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 4px 0px;
}

/* Toolbar styling */
QToolBar {
    background-color: #ffffff;
    border: none;
    border-bottom: 1px solid #e0e0e0;
    spacing: 4px;
    padding: 4px;
}

QToolBar::separator {
    background-color: #e0e0e0;
    width: 1px;
    margin: 4px 2px;
}

/* Status bar styling */
QStatusBar {
    background-color: #ffffff;
    border-top: 1px solid #e0e0e0;
    color: #666666;
}

/* Button styling */
QPushButton {
    background-color: #1976d2;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #1565c0;
}

QPushButton:pressed {
    background-color: #0d47a1;
}

QPushButton:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
}

QPushButton:default {
    background-color: #2196f3;
}

QPushButton:default:hover {
    background-color: #1976d2;
}

/* Secondary button styling */
QPushButton[class="secondary"] {
    background-color: #ffffff;
    color: #1976d2;
    border: 1px solid #1976d2;
}

QPushButton[class="secondary"]:hover {
    background-color: #e3f2fd;
}

/* Tab widget styling */
QTabWidget::pane {
    border: 1px solid #e0e0e0;
    background-color: #ffffff;
    border-radius: 4px;
}

QTabBar::tab {
    background-color: #f5f5f5;
    color: #666666;
    padding: 10px 20px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid #e0e0e0;
    border-bottom: none;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
}

QTabBar::tab:hover:!selected {
    background-color: #eeeeee;
}

/* Table widget styling */
QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #f9f9f9;
    gridline-color: #e0e0e0;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    selection-background-color: #e3f2fd;
    selection-color: #1976d2;
}

QTableWidget::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QHeaderView::section {
    background-color: #f5f5f5;
    color: #333333;
    padding: 8px;
    border: none;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 600;
}

QHeaderView::section:hover {
    background-color: #eeeeee;
}

/* Input field styling */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    color: #333333;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #1976d2;
    outline: none;
}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
    background-color: #f5f5f5;
    color: #9e9e9e;
}

/* Combo box styling */
QComboBox {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    min-width: 120px;
}

QComboBox:focus {
    border-color: #1976d2;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(resources/icons/arrow-down.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    selection-background-color: #e3f2fd;
    selection-color: #1976d2;
}

/* Spin box styling */
QSpinBox, QDoubleSpinBox {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #1976d2;
}

/* Date edit styling */
QDateEdit {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
}

QDateEdit:focus {
    border-color: #1976d2;
}

/* Check box styling */
QCheckBox {
    color: #333333;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-radius: 3px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #1976d2;
    border-color: #1976d2;
    image: url(resources/icons/check.png);
}

QCheckBox::indicator:hover {
    border-color: #1976d2;
}

/* Radio button styling */
QRadioButton {
    color: #333333;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background-color: #ffffff;
}

QRadioButton::indicator:checked {
    background-color: #1976d2;
    border-color: #1976d2;
}

QRadioButton::indicator:hover {
    border-color: #1976d2;
}

/* Group box styling */
QGroupBox {
    font-weight: 600;
    color: #333333;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-top: 8px;
    padding-top: 8px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 0 8px 0 8px;
    background-color: #f5f5f5;
}

/* Frame styling */
QFrame[frameShape="1"] { /* StyledPanel */
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #ffffff;
}

/* Progress bar styling */
QProgressBar {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    text-align: center;
    background-color: #f5f5f5;
}

QProgressBar::chunk {
    background-color: #1976d2;
    border-radius: 3px;
}

/* Slider styling */
QSlider::groove:horizontal {
    border: 1px solid #e0e0e0;
    height: 6px;
    background-color: #f5f5f5;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #1976d2;
    border: 1px solid #1565c0;
    width: 16px;
    margin: -6px 0;
    border-radius: 8px;
}

QSlider::handle:horizontal:hover {
    background-color: #1565c0;
}

/* Scroll bar styling */
QScrollBar:vertical {
    background-color: #f5f5f5;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* Dialog styling */
QDialog {
    background-color: #ffffff;
}

/* Message box styling */
QMessageBox {
    background-color: #ffffff;
}

QMessageBox QPushButton {
    min-width: 80px;
    padding: 6px 16px;
}

/* Tooltip styling */
QToolTip {
    background-color: #333333;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
}
