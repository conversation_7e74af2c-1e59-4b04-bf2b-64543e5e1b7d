"""
Base model class for all SQLAlchemy models in the EDMS application.

Provides common functionality including timestamps, soft delete,
and basic CRUD operations for all models.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Type, TypeVar

from sqlalchemy import Column, DateTime, Boolean, Integer, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session

from app.db.session import get_db_session

logger = logging.getLogger(__name__)

# Create the declarative base
Base = declarative_base()

# Type variable for model classes
ModelType = TypeVar('ModelType', bound='BaseModel')

class BaseModel(Base):
    """
    Abstract base model class that provides common functionality
    for all database models.
    """
    __abstract__ = True
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Soft delete
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def to_dict(self, exclude_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Convert model instance to dictionary.
        
        Args:
            exclude_fields: List of field names to exclude from the dictionary
            
        Returns:
            Dictionary representation of the model
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                # Convert datetime objects to ISO format strings
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude_fields: Optional[List[str]] = None) -> None:
        """
        Update model instance from dictionary.
        
        Args:
            data: Dictionary containing field values
            exclude_fields: List of field names to exclude from update
        """
        exclude_fields = exclude_fields or ['id', 'created_at']
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
        
        self.updated_at = datetime.utcnow()
    
    def soft_delete(self) -> None:
        """Mark the record as deleted without actually removing it."""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.is_deleted = False
        self.deleted_at = None
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def create(cls: Type[ModelType], **kwargs) -> ModelType:
        """
        Create a new instance of the model.
        
        Args:
            **kwargs: Field values for the new instance
            
        Returns:
            New model instance
        """
        instance = cls(**kwargs)
        with get_db_session() as session:
            session.add(instance)
            session.flush()  # Flush to get the ID
            session.refresh(instance)
        return instance
    
    @classmethod
    def get_by_id(cls: Type[ModelType], id: int, include_deleted: bool = False) -> Optional[ModelType]:
        """
        Get a model instance by ID.
        
        Args:
            id: The ID to search for
            include_deleted: Whether to include soft-deleted records
            
        Returns:
            Model instance or None if not found
        """
        with get_db_session() as session:
            query = session.query(cls).filter(cls.id == id)
            if not include_deleted:
                query = query.filter(cls.is_deleted == False)
            return query.first()
    
    @classmethod
    def get_all(cls: Type[ModelType], include_deleted: bool = False, limit: Optional[int] = None) -> List[ModelType]:
        """
        Get all instances of the model.
        
        Args:
            include_deleted: Whether to include soft-deleted records
            limit: Maximum number of records to return
            
        Returns:
            List of model instances
        """
        with get_db_session() as session:
            query = session.query(cls)
            if not include_deleted:
                query = query.filter(cls.is_deleted == False)
            if limit:
                query = query.limit(limit)
            return query.all()
    
    @classmethod
    def count(cls: Type[ModelType], include_deleted: bool = False) -> int:
        """
        Count the number of records.
        
        Args:
            include_deleted: Whether to include soft-deleted records
            
        Returns:
            Number of records
        """
        with get_db_session() as session:
            query = session.query(cls)
            if not include_deleted:
                query = query.filter(cls.is_deleted == False)
            return query.count()
    
    def save(self) -> None:
        """Save the current instance to the database."""
        with get_db_session() as session:
            session.merge(self)
    
    def delete(self, soft: bool = True) -> None:
        """
        Delete the current instance.
        
        Args:
            soft: If True, perform soft delete; if False, hard delete
        """
        if soft:
            self.soft_delete()
            self.save()
        else:
            with get_db_session() as session:
                session.delete(self)
    
    @classmethod
    def bulk_create(cls: Type[ModelType], instances: List[Dict[str, Any]]) -> List[ModelType]:
        """
        Create multiple instances efficiently.
        
        Args:
            instances: List of dictionaries containing field values
            
        Returns:
            List of created model instances
        """
        objects = [cls(**data) for data in instances]
        with get_db_session() as session:
            session.add_all(objects)
            session.flush()
            for obj in objects:
                session.refresh(obj)
        return objects
    
    @classmethod
    def bulk_update(cls: Type[ModelType], updates: List[Dict[str, Any]]) -> None:
        """
        Update multiple instances efficiently.
        
        Args:
            updates: List of dictionaries containing 'id' and field values
        """
        with get_db_session() as session:
            for update_data in updates:
                if 'id' not in update_data:
                    continue
                
                instance_id = update_data.pop('id')
                update_data['updated_at'] = datetime.utcnow()
                
                session.query(cls).filter(cls.id == instance_id).update(update_data)

# Event listeners for automatic timestamp updates
@event.listens_for(BaseModel, 'before_update', propagate=True)
def receive_before_update(mapper, connection, target):
    """Update the updated_at timestamp before any update."""
    target.updated_at = datetime.utcnow()

@event.listens_for(BaseModel, 'before_insert', propagate=True)
def receive_before_insert(mapper, connection, target):
    """Set timestamps before insert."""
    now = datetime.utcnow()
    target.created_at = now
    target.updated_at = now
