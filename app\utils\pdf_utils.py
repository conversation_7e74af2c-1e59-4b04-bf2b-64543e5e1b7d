"""
PDF utility functions for the EDMS application.

Provides PDF text extraction, metadata reading, page counting,
and thumbnail generation for PDF documents.
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

def extract_text_from_pdf(pdf_path: Path) -> Dict[str, Any]:
    """
    Extract text content from PDF file.

    Args:
        pdf_path: Path to PDF file

    Returns:
        Dictionary with extraction results
    """
    result = {
        'success': False,
        'text': '',
        'page_count': 0,
        'method': None,
        'error': None
    }

    if not pdf_path.exists():
        result['error'] = 'PDF file does not exist'
        return result

    # Try PyPDF2 first (faster for text-based PDFs)
    try:
        import PyPDF2

        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            result['page_count'] = len(pdf_reader.pages)

            text_content = []
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content.append(f"--- Page {page_num + 1} ---")
                        text_content.append(page_text)
                except Exception as e:
                    logger.warning(f"Error extracting text from page {page_num + 1}: {e}")

            if text_content:
                result.update({
                    'success': True,
                    'text': '\n\n'.join(text_content),
                    'method': 'PyPDF2'
                })
                return result

    except ImportError:
        logger.warning("PyPDF2 not available, trying alternative methods")
    except Exception as e:
        logger.warning(f"PyPDF2 extraction failed: {e}")

    # Try pdfplumber as fallback
    try:
        import pdfplumber

        text_content = []
        with pdfplumber.open(pdf_path) as pdf:
            result['page_count'] = len(pdf.pages)

            for page_num, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content.append(f"--- Page {page_num + 1} ---")
                        text_content.append(page_text)
                except Exception as e:
                    logger.warning(f"Error extracting text from page {page_num + 1}: {e}")

        if text_content:
            result.update({
                'success': True,
                'text': '\n\n'.join(text_content),
                'method': 'pdfplumber'
            })
            return result

    except ImportError:
        logger.warning("pdfplumber not available")
    except Exception as e:
        logger.warning(f"pdfplumber extraction failed: {e}")

    # If text extraction failed, the PDF might be image-based
    result['error'] = 'Could not extract text from PDF (may be image-based)'
    return result

def get_pdf_metadata(pdf_path: Path) -> Dict[str, Any]:
    """
    Extract metadata from PDF file.
    
    Args:
        pdf_path: Path to PDF file
        
    Returns:
        Dictionary with PDF metadata
    """
    metadata = {
        'title': None,
        'author': None,
        'subject': None,
        'creator': None,
        'producer': None,
        'creation_date': None,
        'modification_date': None,
        'page_count': 0,
        'encrypted': False,
        'error': None
    }
    
    if not pdf_path.exists():
        metadata['error'] = 'PDF file does not exist'
        return metadata
    
    try:
        import PyPDF2
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            # Basic info
            metadata['page_count'] = len(pdf_reader.pages)
            metadata['encrypted'] = pdf_reader.is_encrypted
            
            # Document info
            if pdf_reader.metadata:
                doc_info = pdf_reader.metadata
                
                metadata.update({
                    'title': doc_info.get('/Title'),
                    'author': doc_info.get('/Author'),
                    'subject': doc_info.get('/Subject'),
                    'creator': doc_info.get('/Creator'),
                    'producer': doc_info.get('/Producer'),
                    'creation_date': doc_info.get('/CreationDate'),
                    'modification_date': doc_info.get('/ModDate')
                })
    
    except ImportError:
        metadata['error'] = 'PyPDF2 not available for metadata extraction'
    except Exception as e:
        metadata['error'] = f'Metadata extraction failed: {str(e)}'
        logger.error(f"PDF metadata extraction error for {pdf_path}: {e}")
    
    return metadata

def count_pdf_pages(pdf_path: Path) -> int:
    """
    Count number of pages in PDF.
    
    Args:
        pdf_path: Path to PDF file
        
    Returns:
        Number of pages, or 0 if error
    """
    if not pdf_path.exists():
        return 0
    
    try:
        import PyPDF2
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            return len(pdf_reader.pages)
    
    except ImportError:
        logger.warning("PyPDF2 not available for page counting")
    except Exception as e:
        logger.error(f"Error counting PDF pages for {pdf_path}: {e}")
    
    return 0

def create_pdf_thumbnail(pdf_path: Path, output_path: Path, 
                        page_number: int = 0, size: tuple = (150, 150)) -> Dict[str, Any]:
    """
    Create thumbnail image from PDF page.
    
    Args:
        pdf_path: Path to PDF file
        output_path: Path for output thumbnail
        page_number: Page number to use for thumbnail (0-based)
        size: Thumbnail size (width, height)
        
    Returns:
        Dictionary with thumbnail creation results
    """
    result = {
        'success': False,
        'thumbnail_path': None,
        'error': None
    }
    
    if not pdf_path.exists():
        result['error'] = 'PDF file does not exist'
        return result
    
    try:
        import pdf2image
        from PIL import Image
        
        # Convert specific page to image
        pages = pdf2image.convert_from_path(
            pdf_path, 
            first_page=page_number + 1,
            last_page=page_number + 1,
            dpi=150
        )
        
        if not pages:
            result['error'] = 'Could not convert PDF page to image'
            return result
        
        # Get the page image
        page_image = pages[0]
        
        # Create thumbnail
        page_image.thumbnail(size, Image.Resampling.LANCZOS)
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save thumbnail
        page_image.save(output_path, 'JPEG', quality=85, optimize=True)
        
        result.update({
            'success': True,
            'thumbnail_path': str(output_path)
        })
        
        logger.info(f"PDF thumbnail created: {output_path}")
    
    except ImportError:
        result['error'] = 'pdf2image library not available for thumbnail creation'
    except Exception as e:
        result['error'] = f'Thumbnail creation failed: {str(e)}'
        logger.error(f"PDF thumbnail creation error for {pdf_path}: {e}")
    
    return result

def split_pdf(pdf_path: Path, output_dir: Path, 
              pages_per_file: int = 1) -> Dict[str, Any]:
    """
    Split PDF into multiple files.
    
    Args:
        pdf_path: Path to source PDF
        output_dir: Directory for output files
        pages_per_file: Number of pages per output file
        
    Returns:
        Dictionary with split results
    """
    result = {
        'success': False,
        'output_files': [],
        'total_pages': 0,
        'files_created': 0,
        'error': None
    }
    
    if not pdf_path.exists():
        result['error'] = 'PDF file does not exist'
        return result
    
    try:
        import PyPDF2
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            result['total_pages'] = len(pdf_reader.pages)
            
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Split PDF
            current_file = 0
            pages_in_current_file = 0
            pdf_writer = PyPDF2.PdfWriter()
            
            for page_num, page in enumerate(pdf_reader.pages):
                pdf_writer.add_page(page)
                pages_in_current_file += 1
                
                # Save file when we reach the page limit or it's the last page
                if pages_in_current_file >= pages_per_file or page_num == len(pdf_reader.pages) - 1:
                    output_filename = f"{pdf_path.stem}_part_{current_file + 1:03d}.pdf"
                    output_path = output_dir / output_filename
                    
                    with open(output_path, 'wb') as output_file:
                        pdf_writer.write(output_file)
                    
                    result['output_files'].append(str(output_path))
                    result['files_created'] += 1
                    
                    # Reset for next file
                    pdf_writer = PyPDF2.PdfWriter()
                    pages_in_current_file = 0
                    current_file += 1
            
            result['success'] = True
            logger.info(f"PDF split into {result['files_created']} files")
    
    except ImportError:
        result['error'] = 'PyPDF2 not available for PDF splitting'
    except Exception as e:
        result['error'] = f'PDF splitting failed: {str(e)}'
        logger.error(f"PDF split error for {pdf_path}: {e}")
    
    return result

def merge_pdfs(pdf_paths: List[Path], output_path: Path) -> Dict[str, Any]:
    """
    Merge multiple PDF files into one.
    
    Args:
        pdf_paths: List of PDF file paths to merge
        output_path: Path for merged PDF output
        
    Returns:
        Dictionary with merge results
    """
    result = {
        'success': False,
        'total_pages': 0,
        'files_merged': 0,
        'error': None
    }
    
    if not pdf_paths:
        result['error'] = 'No PDF files provided for merging'
        return result
    
    try:
        import PyPDF2
        
        pdf_writer = PyPDF2.PdfWriter()
        
        for pdf_path in pdf_paths:
            if not pdf_path.exists():
                logger.warning(f"PDF file not found, skipping: {pdf_path}")
                continue
            
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    for page in pdf_reader.pages:
                        pdf_writer.add_page(page)
                        result['total_pages'] += 1
                    
                    result['files_merged'] += 1
            
            except Exception as e:
                logger.warning(f"Error processing {pdf_path}: {e}")
                continue
        
        if result['files_merged'] == 0:
            result['error'] = 'No valid PDF files could be processed'
            return result
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write merged PDF
        with open(output_path, 'wb') as output_file:
            pdf_writer.write(output_file)
        
        result['success'] = True
        logger.info(f"Merged {result['files_merged']} PDFs into {output_path}")
    
    except ImportError:
        result['error'] = 'PyPDF2 not available for PDF merging'
    except Exception as e:
        result['error'] = f'PDF merging failed: {str(e)}'
        logger.error(f"PDF merge error: {e}")
    
    return result

def validate_pdf(pdf_path: Path) -> Dict[str, Any]:
    """
    Validate PDF file integrity.
    
    Args:
        pdf_path: Path to PDF file
        
    Returns:
        Dictionary with validation results
    """
    result = {
        'valid': False,
        'page_count': 0,
        'encrypted': False,
        'has_text': False,
        'file_size': 0,
        'error': None
    }
    
    if not pdf_path.exists():
        result['error'] = 'PDF file does not exist'
        return result
    
    try:
        result['file_size'] = pdf_path.stat().st_size
        
        import PyPDF2
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            result['page_count'] = len(pdf_reader.pages)
            result['encrypted'] = pdf_reader.is_encrypted
            
            # Check if PDF has extractable text
            if result['page_count'] > 0:
                try:
                    first_page_text = pdf_reader.pages[0].extract_text()
                    result['has_text'] = bool(first_page_text.strip())
                except Exception:
                    result['has_text'] = False
            
            result['valid'] = True
    
    except ImportError:
        result['error'] = 'PyPDF2 not available for PDF validation'
    except Exception as e:
        result['error'] = f'PDF validation failed: {str(e)}'
        logger.error(f"PDF validation error for {pdf_path}: {e}")
    
    return result

def get_pdf_info(pdf_path: Path) -> Dict[str, Any]:
    """
    Get comprehensive PDF information.
    
    Args:
        pdf_path: Path to PDF file
        
    Returns:
        Dictionary with PDF information
    """
    info = {
        'file_exists': False,
        'file_size': 0,
        'valid_pdf': False,
        'page_count': 0,
        'encrypted': False,
        'has_text': False,
        'metadata': {},
        'error': None
    }
    
    if not pdf_path.exists():
        info['error'] = 'PDF file does not exist'
        return info
    
    info['file_exists'] = True
    info['file_size'] = pdf_path.stat().st_size
    
    # Validate PDF
    validation = validate_pdf(pdf_path)
    info.update({
        'valid_pdf': validation['valid'],
        'page_count': validation['page_count'],
        'encrypted': validation['encrypted'],
        'has_text': validation['has_text']
    })
    
    if validation['error']:
        info['error'] = validation['error']
        return info
    
    # Get metadata
    metadata = get_pdf_metadata(pdf_path)
    if not metadata.get('error'):
        info['metadata'] = {k: v for k, v in metadata.items() if k != 'error'}
    
    return info
