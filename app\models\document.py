"""
Document model for file management in the EDMS application.

Provides document storage, metadata management, versioning,
and full-text search capabilities.
"""

import logging
import os
from datetime import datetime, date
from enum import Enum
from pathlib import Path
from typing import List, Optional, Dict, Any

from sqlalchemy import Column, String, Integer, DateTime, Date, Text, <PERSON>olean, <PERSON><PERSON>ey, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property

from app.models.base import BaseModel

logger = logging.getLogger(__name__)

class DocumentStatus(Enum):
    """Document lifecycle status."""
    DRAFT = "draft"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    ARCHIVED = "archived"
    EXPIRED = "expired"

class DocumentType(Enum):
    """Document type categories."""
    PDF = "pdf"
    WORD = "word"
    EXCEL = "excel"
    POWERPOINT = "powerpoint"
    IMAGE = "image"
    TEXT = "text"
    OTHER = "other"

class Document(BaseModel):
    """
    Document model for file management and metadata.
    
    Attributes:
        title: Document title
        description: Document description
        file_name: Original file name
        file_path: Path to stored file
        file_size: File size in bytes
        file_hash: SHA-256 hash of file content
        mime_type: MIME type of the file
        document_type: Categorized document type
        status: Document lifecycle status
        organization: Organization or department
        keywords: Searchable keywords (JSON array)
        file_number: Unique file/reference number
        document_date: Date of the document (not creation date)
        expiration_date: Document expiration date
        retention_date: Date until which document should be retained
        version: Document version number
        parent_document_id: ID of parent document (for versioning)
        owner_id: ID of user who owns the document
        extracted_text: Full text extracted from document
        thumbnail_path: Path to document thumbnail
        page_count: Number of pages (for multi-page documents)
        metadata: Additional metadata (JSON)
    """
    __tablename__ = 'documents'
    
    # Basic document information
    title = Column(String(500), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # File information
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(1000), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_hash = Column(String(64), nullable=False, index=True)  # SHA-256
    mime_type = Column(String(100), nullable=False)
    document_type = Column(String(20), nullable=False, index=True)
    
    # Document metadata
    status = Column(String(20), default=DocumentStatus.DRAFT.value, nullable=False, index=True)
    organization = Column(String(200), nullable=True, index=True)
    keywords = Column(JSON, nullable=True)  # Array of keywords
    file_number = Column(String(100), nullable=True, unique=True, index=True)
    
    # Dates
    document_date = Column(Date, nullable=True, index=True)
    expiration_date = Column(Date, nullable=True, index=True)
    retention_date = Column(Date, nullable=True, index=True)
    
    # Versioning
    version = Column(Float, default=1.0, nullable=False)
    parent_document_id = Column(Integer, ForeignKey('documents.id'), nullable=True)
    
    # Ownership
    owner_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    
    # Content and processing
    extracted_text = Column(Text, nullable=True)  # Full-text search
    thumbnail_path = Column(String(1000), nullable=True)
    page_count = Column(Integer, nullable=True)
    
    # Additional metadata
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    owner = relationship("User", back_populates="documents")
    parent_document = relationship("Document", remote_side="Document.id", backref="versions")
    audit_logs = relationship("AuditLog", back_populates="document", lazy="dynamic")
    
    def __repr__(self) -> str:
        return f"<Document(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    @hybrid_property
    def is_expired(self) -> bool:
        """Check if document has expired."""
        if self.expiration_date is None:
            return False
        return date.today() > self.expiration_date
    
    @hybrid_property
    def is_near_expiration(self, days: int = 30) -> bool:
        """Check if document is near expiration."""
        if self.expiration_date is None:
            return False
        from datetime import timedelta
        return date.today() + timedelta(days=days) >= self.expiration_date
    
    @hybrid_property
    def file_extension(self) -> str:
        """Get file extension."""
        return Path(self.file_name).suffix.lower()
    
    @hybrid_property
    def file_size_mb(self) -> float:
        """Get file size in megabytes."""
        return round(self.file_size / (1024 * 1024), 2)
    
    def get_absolute_file_path(self) -> Path:
        """Get absolute path to the document file."""
        from config import get_config
        config = get_config()
        base_path = config['storage']['documents_path']
        return base_path / self.file_path
    
    def get_absolute_thumbnail_path(self) -> Optional[Path]:
        """Get absolute path to the document thumbnail."""
        if not self.thumbnail_path:
            return None
        
        from config import get_config
        config = get_config()
        base_path = config['storage']['thumbnails_path']
        return base_path / self.thumbnail_path
    
    def file_exists(self) -> bool:
        """Check if the document file exists on disk."""
        try:
            return self.get_absolute_file_path().exists()
        except Exception as e:
            logger.error(f"Error checking file existence for document {self.id}: {e}")
            return False
    
    def thumbnail_exists(self) -> bool:
        """Check if the document thumbnail exists on disk."""
        try:
            thumbnail_path = self.get_absolute_thumbnail_path()
            return thumbnail_path is not None and thumbnail_path.exists()
        except Exception as e:
            logger.error(f"Error checking thumbnail existence for document {self.id}: {e}")
            return False
    
    def add_keyword(self, keyword: str) -> None:
        """Add a keyword to the document."""
        if not self.keywords:
            self.keywords = []
        
        keyword = keyword.strip().lower()
        if keyword and keyword not in self.keywords:
            self.keywords.append(keyword)
            self.save()
    
    def remove_keyword(self, keyword: str) -> None:
        """Remove a keyword from the document."""
        if not self.keywords:
            return
        
        keyword = keyword.strip().lower()
        if keyword in self.keywords:
            self.keywords.remove(keyword)
            self.save()
    
    def set_keywords(self, keywords: List[str]) -> None:
        """Set document keywords."""
        self.keywords = [kw.strip().lower() for kw in keywords if kw.strip()]
        self.save()
    
    def update_status(self, new_status: DocumentStatus) -> None:
        """Update document status."""
        old_status = self.status
        self.status = new_status.value
        self.save()
        logger.info(f"Document {self.id} status changed from {old_status} to {new_status.value}")
    
    def create_version(self, new_file_path: str, new_file_size: int, 
                      new_file_hash: str, version_increment: float = 0.1) -> 'Document':
        """
        Create a new version of this document.
        
        Args:
            new_file_path: Path to the new version file
            new_file_size: Size of the new version file
            new_file_hash: Hash of the new version file
            version_increment: Version increment (default: 0.1)
            
        Returns:
            New Document instance representing the new version
        """
        new_version = Document(
            title=self.title,
            description=self.description,
            file_name=self.file_name,
            file_path=new_file_path,
            file_size=new_file_size,
            file_hash=new_file_hash,
            mime_type=self.mime_type,
            document_type=self.document_type,
            status=DocumentStatus.DRAFT.value,
            organization=self.organization,
            keywords=self.keywords.copy() if self.keywords else None,
            file_number=self.file_number,
            document_date=self.document_date,
            expiration_date=self.expiration_date,
            retention_date=self.retention_date,
            version=self.version + version_increment,
            parent_document_id=self.id,
            owner_id=self.owner_id,
            metadata=self.metadata.copy() if self.metadata else None
        )
        new_version.save()
        
        logger.info(f"New version {new_version.version} created for document {self.id}")
        return new_version
    
    def get_all_versions(self) -> List['Document']:
        """Get all versions of this document."""
        from app.db.session import get_db_session
        
        with get_db_session() as session:
            # Get the root document
            root_id = self.parent_document_id if self.parent_document_id else self.id
            
            # Get all versions including the root
            versions = session.query(Document).filter(
                (Document.id == root_id) | (Document.parent_document_id == root_id),
                Document.is_deleted == False
            ).order_by(Document.version).all()
            
            return versions
    
    def get_latest_version(self) -> 'Document':
        """Get the latest version of this document."""
        versions = self.get_all_versions()
        return max(versions, key=lambda v: v.version) if versions else self
    
    @classmethod
    def search(cls, query: str, filters: Optional[Dict[str, Any]] = None, 
               limit: int = 100, offset: int = 0) -> List['Document']:
        """
        Search documents by text and filters.
        
        Args:
            query: Search query string
            filters: Additional filters (status, organization, date_range, etc.)
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of matching documents
        """
        from app.db.session import get_db_session
        from sqlalchemy import or_, and_
        
        with get_db_session() as session:
            # Base query
            db_query = session.query(cls).filter(cls.is_deleted == False)
            
            # Text search
            if query:
                search_conditions = []
                search_terms = query.lower().split()
                
                for term in search_terms:
                    term_conditions = or_(
                        cls.title.ilike(f'%{term}%'),
                        cls.description.ilike(f'%{term}%'),
                        cls.file_name.ilike(f'%{term}%'),
                        cls.organization.ilike(f'%{term}%'),
                        cls.extracted_text.ilike(f'%{term}%')
                    )
                    search_conditions.append(term_conditions)
                
                if search_conditions:
                    db_query = db_query.filter(and_(*search_conditions))
            
            # Apply filters
            if filters:
                if 'status' in filters:
                    db_query = db_query.filter(cls.status == filters['status'])
                
                if 'organization' in filters:
                    db_query = db_query.filter(cls.organization.ilike(f"%{filters['organization']}%"))
                
                if 'document_type' in filters:
                    db_query = db_query.filter(cls.document_type == filters['document_type'])
                
                if 'owner_id' in filters:
                    db_query = db_query.filter(cls.owner_id == filters['owner_id'])
                
                if 'date_from' in filters:
                    db_query = db_query.filter(cls.document_date >= filters['date_from'])
                
                if 'date_to' in filters:
                    db_query = db_query.filter(cls.document_date <= filters['date_to'])
                
                if 'keywords' in filters:
                    # Search in keywords JSON array
                    for keyword in filters['keywords']:
                        db_query = db_query.filter(cls.keywords.contains([keyword]))
            
            # Order by relevance (updated_at desc for now)
            db_query = db_query.order_by(cls.updated_at.desc())
            
            # Apply pagination
            db_query = db_query.offset(offset).limit(limit)
            
            return db_query.all()
    
    @classmethod
    def get_expiring_documents(cls, days: int = 30) -> List['Document']:
        """Get documents expiring within specified days."""
        from app.db.session import get_db_session
        from datetime import timedelta
        
        cutoff_date = date.today() + timedelta(days=days)
        
        with get_db_session() as session:
            return session.query(cls).filter(
                cls.is_deleted == False,
                cls.expiration_date.isnot(None),
                cls.expiration_date <= cutoff_date,
                cls.expiration_date >= date.today()
            ).order_by(cls.expiration_date).all()
    
    @classmethod
    def get_expired_documents(cls) -> List['Document']:
        """Get all expired documents."""
        from app.db.session import get_db_session
        
        with get_db_session() as session:
            return session.query(cls).filter(
                cls.is_deleted == False,
                cls.expiration_date.isnot(None),
                cls.expiration_date < date.today()
            ).order_by(cls.expiration_date).all()
    
    def to_dict(self, exclude_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """Convert document to dictionary with computed properties."""
        result = super().to_dict(exclude_fields=exclude_fields)
        
        # Add computed properties
        result['is_expired'] = self.is_expired
        result['is_near_expiration'] = self.is_near_expiration
        result['file_extension'] = self.file_extension
        result['file_size_mb'] = self.file_size_mb
        result['file_exists'] = self.file_exists()
        result['thumbnail_exists'] = self.thumbnail_exists()
        
        # Add owner information if available
        if self.owner:
            result['owner_name'] = self.owner.full_name
            result['owner_username'] = self.owner.username
        
        return result
