"""
User model for authentication and authorization in the EDMS application.

Provides user account management with role-based access control,
password hashing, and session management.
"""

import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import List, Optional

import bcrypt
from sqlalchemy import Column, String, Enum as <PERSON><PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, DateT<PERSON>, Boolean, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel

logger = logging.getLogger(__name__)

class UserRole(Enum):
    """User roles for access control."""
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"

class UserStatus(Enum):
    """User account status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    LOCKED = "locked"
    SUSPENDED = "suspended"

class User(BaseModel):
    """
    User model for authentication and authorization.
    
    Attributes:
        username: Unique username for login
        email: User's email address
        password_hash: Hashed password using bcrypt
        first_name: User's first name
        last_name: User's last name
        role: User's role (admin, user, viewer)
        status: Account status (active, inactive, locked, suspended)
        last_login: Timestamp of last successful login
        failed_login_attempts: Number of consecutive failed login attempts
        locked_until: Timestamp until which account is locked
        password_changed_at: Timestamp of last password change
        notes: Administrative notes about the user
    """
    __tablename__ = 'users'
    
    # Basic user information
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # Personal information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    
    # Role and permissions
    role = Column(SQLEnum(UserRole), default=UserRole.USER, nullable=False)
    status = Column(SQLEnum(UserStatus), default=UserStatus.ACTIVE, nullable=False)
    
    # Authentication tracking
    last_login = Column(DateTime, nullable=True)
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime, nullable=True)
    password_changed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Administrative
    notes = Column(Text, nullable=True)
    
    # Relationships
    audit_logs = relationship("AuditLog", back_populates="user", lazy="dynamic")
    documents = relationship("Document", back_populates="owner", lazy="dynamic")
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', role='{self.role.value}')>"
    
    @property
    def full_name(self) -> str:
        """Get the user's full name."""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def is_admin(self) -> bool:
        """Check if user has admin role."""
        return self.role == UserRole.ADMIN
    
    @property
    def is_active(self) -> bool:
        """Check if user account is active."""
        return self.status == UserStatus.ACTIVE and not self.is_locked
    
    @property
    def is_locked(self) -> bool:
        """Check if user account is currently locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def set_password(self, password: str) -> None:
        """
        Set user password with bcrypt hashing.
        
        Args:
            password: Plain text password
        """
        if not password:
            raise ValueError("Password cannot be empty")
        
        # Generate salt and hash password
        salt = bcrypt.gensalt()
        password_bytes = password.encode('utf-8')
        self.password_hash = bcrypt.hashpw(password_bytes, salt).decode('utf-8')
        self.password_changed_at = datetime.utcnow()
        self.failed_login_attempts = 0  # Reset failed attempts on password change
        
        logger.info(f"Password updated for user: {self.username}")
    
    def check_password(self, password: str) -> bool:
        """
        Verify password against stored hash.
        
        Args:
            password: Plain text password to verify
            
        Returns:
            True if password is correct, False otherwise
        """
        if not password or not self.password_hash:
            return False
        
        try:
            password_bytes = password.encode('utf-8')
            hash_bytes = self.password_hash.encode('utf-8')
            return bcrypt.checkpw(password_bytes, hash_bytes)
        except Exception as e:
            logger.error(f"Password verification error for user {self.username}: {e}")
            return False
    
    def authenticate(self, password: str) -> bool:
        """
        Authenticate user with password and update login tracking.
        
        Args:
            password: Plain text password
            
        Returns:
            True if authentication successful, False otherwise
        """
        # Check if account is locked
        if self.is_locked:
            logger.warning(f"Login attempt for locked user: {self.username}")
            return False
        
        # Check if account is active
        if not self.is_active:
            logger.warning(f"Login attempt for inactive user: {self.username}")
            return False
        
        # Verify password
        if self.check_password(password):
            # Successful login
            self.last_login = datetime.utcnow()
            self.failed_login_attempts = 0
            self.locked_until = None
            self.save()
            
            logger.info(f"Successful login for user: {self.username}")
            return True
        else:
            # Failed login
            self.failed_login_attempts += 1
            
            # Lock account if too many failed attempts
            from config import get_config
            config = get_config()
            max_attempts = config['security']['max_login_attempts']
            lockout_duration = config['security']['lockout_duration_minutes']
            
            if self.failed_login_attempts >= max_attempts:
                self.locked_until = datetime.utcnow() + timedelta(minutes=lockout_duration)
                logger.warning(f"User account locked due to failed attempts: {self.username}")
            
            self.save()
            logger.warning(f"Failed login attempt for user: {self.username} (attempt {self.failed_login_attempts})")
            return False
    
    def unlock_account(self) -> None:
        """Unlock user account and reset failed login attempts."""
        self.locked_until = None
        self.failed_login_attempts = 0
        self.save()
        logger.info(f"User account unlocked: {self.username}")
    
    def change_role(self, new_role: UserRole) -> None:
        """
        Change user role.
        
        Args:
            new_role: New role to assign
        """
        old_role = self.role
        self.role = new_role
        self.save()
        logger.info(f"User role changed: {self.username} from {old_role.value} to {new_role.value}")
    
    def change_status(self, new_status: UserStatus) -> None:
        """
        Change user status.
        
        Args:
            new_status: New status to assign
        """
        old_status = self.status
        self.status = new_status
        self.save()
        logger.info(f"User status changed: {self.username} from {old_status.value} to {new_status.value}")
    
    def can_perform_action(self, action: str) -> bool:
        """
        Check if user can perform a specific action based on role.
        
        Args:
            action: Action to check (e.g., 'delete_document', 'manage_users')
            
        Returns:
            True if user can perform action, False otherwise
        """
        if not self.is_active:
            return False
        
        # Admin can do everything
        if self.role == UserRole.ADMIN:
            return True
        
        # Define role permissions
        permissions = {
            UserRole.USER: {
                'upload_document', 'edit_document', 'view_document', 'search_documents',
                'edit_own_profile', 'change_own_password'
            },
            UserRole.VIEWER: {
                'view_document', 'search_documents', 'edit_own_profile', 'change_own_password'
            }
        }
        
        user_permissions = permissions.get(self.role, set())
        return action in user_permissions
    
    @classmethod
    def create_user(cls, username: str, email: str, password: str, first_name: str, 
                   last_name: str, role: UserRole = UserRole.USER) -> 'User':
        """
        Create a new user with password hashing.
        
        Args:
            username: Unique username
            email: User's email address
            password: Plain text password
            first_name: User's first name
            last_name: User's last name
            role: User's role (default: USER)
            
        Returns:
            Created User instance
        """
        user = cls(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            role=role
        )
        user.set_password(password)
        user.save()
        
        logger.info(f"New user created: {username} with role {role.value}")
        return user
    
    @classmethod
    def get_by_username(cls, username: str) -> Optional['User']:
        """
        Get user by username.
        
        Args:
            username: Username to search for
            
        Returns:
            User instance or None if not found
        """
        from app.db.session import get_db_session
        
        with get_db_session() as session:
            return session.query(cls).filter(
                cls.username == username,
                cls.is_deleted == False
            ).first()
    
    @classmethod
    def get_by_email(cls, email: str) -> Optional['User']:
        """
        Get user by email address.
        
        Args:
            email: Email address to search for
            
        Returns:
            User instance or None if not found
        """
        from app.db.session import get_db_session
        
        with get_db_session() as session:
            return session.query(cls).filter(
                cls.email == email,
                cls.is_deleted == False
            ).first()
    
    def to_dict(self, exclude_fields: Optional[List[str]] = None) -> dict:
        """
        Convert user to dictionary, excluding sensitive fields by default.
        
        Args:
            exclude_fields: Additional fields to exclude
            
        Returns:
            Dictionary representation of user
        """
        default_exclude = ['password_hash']
        if exclude_fields:
            default_exclude.extend(exclude_fields)
        
        result = super().to_dict(exclude_fields=default_exclude)
        
        # Add computed properties
        result['full_name'] = self.full_name
        result['is_admin'] = self.is_admin
        result['is_active'] = self.is_active
        result['is_locked'] = self.is_locked
        
        # Convert enums to strings
        result['role'] = self.role.value
        result['status'] = self.status.value
        
        return result
