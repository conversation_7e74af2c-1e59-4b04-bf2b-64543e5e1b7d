"""
Database session management for the EDMS application.

Provides SQLAlchemy engine, session factory, and database initialization utilities.
Supports both regular SQLite and SQLCipher for encryption.
"""

import logging
from contextlib import contextmanager
from typing import Generator, Optional

from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from config import get_config

logger = logging.getLogger(__name__)

# Global variables for database connection
_engine: Optional[Engine] = None
_session_factory: Optional[sessionmaker] = None

def get_engine() -> Engine:
    """
    Get or create the SQLAlchemy engine.
    
    Returns:
        SQLAlchemy Engine instance
    """
    global _engine
    
    if _engine is None:
        config = get_config()
        db_config = config['database']
        security_config = config['security']
        
        # Choose database URL based on encryption setting
        if security_config['use_encryption']:
            # SQLCipher configuration
            db_url = db_config['url'].replace('sqlite://', 'sqlite+pysqlcipher://')
            connect_args = {
                'check_same_thread': False,
                'password': 'your_encryption_key_here'  # In production, use environment variable
            }
        else:
            # Regular SQLite configuration
            db_url = db_config['url']
            connect_args = {'check_same_thread': False}
        
        _engine = create_engine(
            db_url,
            echo=db_config['echo'],
            pool_pre_ping=db_config['pool_pre_ping'],
            pool_recycle=db_config['pool_recycle'],
            poolclass=StaticPool,
            connect_args=connect_args
        )
        
        # Enable foreign key constraints for SQLite
        @event.listens_for(_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.execute("PRAGMA journal_mode=WAL")
            cursor.execute("PRAGMA synchronous=NORMAL")
            cursor.execute("PRAGMA temp_store=MEMORY")
            cursor.execute("PRAGMA mmap_size=268435456")  # 256MB
            cursor.close()
        
        logger.info(f"Database engine created: {db_url}")
    
    return _engine

def get_session_factory() -> sessionmaker:
    """
    Get or create the SQLAlchemy session factory.
    
    Returns:
        SQLAlchemy sessionmaker instance
    """
    global _session_factory
    
    if _session_factory is None:
        engine = get_engine()
        _session_factory = sessionmaker(bind=engine, expire_on_commit=False)
        logger.info("Session factory created")
    
    return _session_factory

def get_session() -> Session:
    """
    Create a new database session.
    
    Returns:
        SQLAlchemy Session instance
    """
    session_factory = get_session_factory()
    return session_factory()

@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """
    Context manager for database sessions with automatic cleanup.
    
    Yields:
        SQLAlchemy Session instance
        
    Example:
        with get_db_session() as session:
            user = session.query(User).first()
    """
    session = get_session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        session.close()

def init_database():
    """
    Initialize the database by creating all tables.
    This should be called once when the application starts.
    """
    from app.models.base import Base
    
    engine = get_engine()
    
    # Import all models to ensure they're registered with Base
    from app.models import document, user, audit
    
    # Create all tables
    Base.metadata.create_all(engine)
    logger.info("Database tables created successfully")

def drop_database():
    """
    Drop all database tables. Use with caution!
    This is primarily for testing and development.
    """
    from app.models.base import Base
    
    engine = get_engine()
    Base.metadata.drop_all(engine)
    logger.warning("All database tables dropped")

def reset_database():
    """
    Reset the database by dropping and recreating all tables.
    Use with caution! This will delete all data.
    """
    drop_database()
    init_database()
    logger.warning("Database reset completed")

def check_database_connection() -> bool:
    """
    Check if the database connection is working.
    
    Returns:
        True if connection is successful, False otherwise
    """
    try:
        engine = get_engine()
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False

def get_database_info() -> dict:
    """
    Get information about the database.
    
    Returns:
        Dictionary containing database information
    """
    try:
        engine = get_engine()
        with engine.connect() as conn:
            # Get SQLite version
            result = conn.execute("SELECT sqlite_version()").fetchone()
            sqlite_version = result[0] if result else "Unknown"
            
            # Get database file size (if using file-based SQLite)
            db_url = str(engine.url)
            if 'sqlite:///' in db_url:
                import os
                db_path = db_url.replace('sqlite:///', '')
                file_size = os.path.getsize(db_path) if os.path.exists(db_path) else 0
            else:
                file_size = 0
            
            return {
                'engine': str(engine.url),
                'sqlite_version': sqlite_version,
                'file_size_bytes': file_size,
                'connection_pool_size': engine.pool.size(),
                'checked_out_connections': engine.pool.checkedout(),
            }
    except Exception as e:
        logger.error(f"Failed to get database info: {e}")
        return {'error': str(e)}

def close_database():
    """
    Close all database connections and clean up resources.
    This should be called when the application shuts down.
    """
    global _engine, _session_factory
    
    if _engine:
        _engine.dispose()
        _engine = None
        logger.info("Database engine disposed")
    
    _session_factory = None
    logger.info("Database connections closed")
