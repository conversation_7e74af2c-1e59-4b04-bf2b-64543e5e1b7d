"""
Authentication and authorization service for the EDMS application.

Handles user authentication, session management, password policies,
and role-based access control.
"""

import logging
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

from app.models.user import User, UserRole, UserStatus
from app.models.audit import AuditLog, AuditAction, AuditLevel
from config import get_config

logger = logging.getLogger(__name__)

class AuthenticationError(Exception):
    """Exception raised for authentication errors."""
    pass

class AuthorizationError(Exception):
    """Exception raised for authorization errors."""
    pass

class SessionManager:
    """Manage user sessions."""
    
    def __init__(self):
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.config = get_config()
        self.session_timeout = self.config['security']['session_timeout_minutes']
    
    def create_session(self, user: User) -> str:
        """
        Create a new session for user.
        
        Args:
            user: User instance
            
        Returns:
            Session ID
        """
        session_id = secrets.token_urlsafe(32)
        session_data = {
            'user_id': user.id,
            'username': user.username,
            'role': user.role.value,
            'created_at': datetime.utcnow(),
            'last_activity': datetime.utcnow(),
            'ip_address': None,  # Set by caller if available
            'user_agent': None   # Set by caller if available
        }
        
        self.active_sessions[session_id] = session_data
        logger.info(f"Session created for user {user.username}: {session_id}")
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session data by session ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session data or None if not found/expired
        """
        if session_id not in self.active_sessions:
            return None
        
        session_data = self.active_sessions[session_id]
        
        # Check if session has expired
        if self._is_session_expired(session_data):
            self.destroy_session(session_id)
            return None
        
        # Update last activity
        session_data['last_activity'] = datetime.utcnow()
        
        return session_data
    
    def destroy_session(self, session_id: str) -> bool:
        """
        Destroy a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session was destroyed, False if not found
        """
        if session_id in self.active_sessions:
            session_data = self.active_sessions.pop(session_id)
            logger.info(f"Session destroyed for user {session_data.get('username', 'unknown')}: {session_id}")
            return True
        
        return False
    
    def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions.
        
        Returns:
            Number of sessions cleaned up
        """
        expired_sessions = []
        
        for session_id, session_data in self.active_sessions.items():
            if self._is_session_expired(session_data):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.destroy_session(session_id)
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
        
        return len(expired_sessions)
    
    def get_user_sessions(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all active sessions for a user."""
        user_sessions = []
        
        for session_id, session_data in self.active_sessions.items():
            if session_data['user_id'] == user_id and not self._is_session_expired(session_data):
                session_info = session_data.copy()
                session_info['session_id'] = session_id
                user_sessions.append(session_info)
        
        return user_sessions
    
    def destroy_user_sessions(self, user_id: int) -> int:
        """Destroy all sessions for a user."""
        sessions_to_destroy = []
        
        for session_id, session_data in self.active_sessions.items():
            if session_data['user_id'] == user_id:
                sessions_to_destroy.append(session_id)
        
        for session_id in sessions_to_destroy:
            self.destroy_session(session_id)
        
        return len(sessions_to_destroy)
    
    def _is_session_expired(self, session_data: Dict[str, Any]) -> bool:
        """Check if session has expired."""
        last_activity = session_data['last_activity']
        expiry_time = last_activity + timedelta(minutes=self.session_timeout)
        return datetime.utcnow() > expiry_time

class AuthService:
    """Authentication and authorization service."""
    
    def __init__(self):
        self.session_manager = SessionManager()
        self.config = get_config()
        self.security_config = self.config['security']
    
    def authenticate(self, username: str, password: str, 
                    ip_address: Optional[str] = None, 
                    user_agent: Optional[str] = None) -> Dict[str, Any]:
        """
        Authenticate user with username and password.
        
        Args:
            username: Username
            password: Password
            ip_address: Client IP address
            user_agent: Client user agent
            
        Returns:
            Dictionary with authentication result
        """
        result = {
            'success': False,
            'user': None,
            'session_id': None,
            'error': None
        }
        
        try:
            # Get user by username
            user = User.get_by_username(username)
            if not user:
                result['error'] = 'Invalid username or password'
                AuditLog.log_action(
                    action=AuditAction.LOGIN_FAILED,
                    description=f"Login failed: user not found ({username})",
                    level=AuditLevel.WARNING,
                    success=False,
                    error_message='User not found',
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                return result
            
            # Attempt authentication
            if user.authenticate(password):
                # Create session
                session_id = self.session_manager.create_session(user)
                
                # Update session with network info
                if session_id in self.session_manager.active_sessions:
                    session_data = self.session_manager.active_sessions[session_id]
                    session_data['ip_address'] = ip_address
                    session_data['user_agent'] = user_agent
                
                result.update({
                    'success': True,
                    'user': user,
                    'session_id': session_id
                })
                
                # Log successful login
                AuditLog.log_login(
                    user_id=user.id,
                    success=True,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
                logger.info(f"User authenticated successfully: {username}")
                
            else:
                result['error'] = 'Invalid username or password'
                
                # Log failed login
                AuditLog.log_login(
                    user_id=user.id,
                    success=False,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    error_message='Invalid password'
                )
                
                logger.warning(f"Authentication failed for user: {username}")
        
        except Exception as e:
            result['error'] = 'Authentication error occurred'
            logger.error(f"Authentication error for {username}: {e}")
        
        return result
    
    def logout(self, session_id: str) -> bool:
        """
        Logout user by destroying session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if successful, False otherwise
        """
        session_data = self.session_manager.get_session(session_id)
        if session_data:
            user_id = session_data['user_id']
            username = session_data['username']
            
            # Destroy session
            success = self.session_manager.destroy_session(session_id)
            
            if success:
                # Log logout
                AuditLog.log_action(
                    action=AuditAction.LOGOUT,
                    description=f"User logged out: {username}",
                    user_id=user_id
                )
                
                logger.info(f"User logged out: {username}")
            
            return success
        
        return False
    
    def get_current_user(self, session_id: str) -> Optional[User]:
        """
        Get current user from session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            User instance or None if session invalid
        """
        session_data = self.session_manager.get_session(session_id)
        if session_data:
            return User.get_by_id(session_data['user_id'])
        
        return None
    
    def require_authentication(self, session_id: str) -> User:
        """
        Require valid authentication, raise exception if not authenticated.
        
        Args:
            session_id: Session identifier
            
        Returns:
            User instance
            
        Raises:
            AuthenticationError: If not authenticated
        """
        user = self.get_current_user(session_id)
        if not user:
            raise AuthenticationError("Authentication required")
        
        return user
    
    def require_role(self, session_id: str, required_role: UserRole) -> User:
        """
        Require specific user role.
        
        Args:
            session_id: Session identifier
            required_role: Required user role
            
        Returns:
            User instance
            
        Raises:
            AuthenticationError: If not authenticated
            AuthorizationError: If insufficient permissions
        """
        user = self.require_authentication(session_id)
        
        # Admin can do everything
        if user.role == UserRole.ADMIN:
            return user
        
        # Check specific role
        if user.role != required_role:
            raise AuthorizationError(f"Role {required_role.value} required")
        
        return user
    
    def require_admin(self, session_id: str) -> User:
        """Require admin role."""
        return self.require_role(session_id, UserRole.ADMIN)
    
    def can_perform_action(self, session_id: str, action: str, 
                          resource_owner_id: Optional[int] = None) -> bool:
        """
        Check if user can perform specific action.
        
        Args:
            session_id: Session identifier
            action: Action to check
            resource_owner_id: ID of resource owner (for ownership checks)
            
        Returns:
            True if action is allowed, False otherwise
        """
        try:
            user = self.get_current_user(session_id)
            if not user:
                return False
            
            # Admin can do everything
            if user.role == UserRole.ADMIN:
                return True
            
            # Check basic role permissions
            if not user.can_perform_action(action):
                return False
            
            # Check ownership for certain actions
            ownership_actions = {
                'edit_document', 'delete_document', 'update_document_status'
            }
            
            if action in ownership_actions and resource_owner_id is not None:
                return user.id == resource_owner_id
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking action permission: {e}")
            return False
    
    def change_password(self, session_id: str, current_password: str, 
                       new_password: str) -> Dict[str, Any]:
        """
        Change user password.
        
        Args:
            session_id: Session identifier
            current_password: Current password
            new_password: New password
            
        Returns:
            Dictionary with operation result
        """
        result = {
            'success': False,
            'error': None
        }
        
        try:
            user = self.require_authentication(session_id)
            
            # Verify current password
            if not user.check_password(current_password):
                result['error'] = 'Current password is incorrect'
                return result
            
            # Validate new password
            validation = self.validate_password(new_password)
            if not validation['valid']:
                result['error'] = '; '.join(validation['errors'])
                return result
            
            # Change password
            user.set_password(new_password)
            
            # Log password change
            AuditLog.log_action(
                action=AuditAction.PASSWORD_CHANGED,
                description=f"Password changed for user: {user.username}",
                user_id=user.id
            )
            
            result['success'] = True
            logger.info(f"Password changed for user: {user.username}")
            
        except AuthenticationError as e:
            result['error'] = str(e)
        except Exception as e:
            result['error'] = 'Password change failed'
            logger.error(f"Password change error: {e}")
        
        return result
    
    def validate_password(self, password: str) -> Dict[str, Any]:
        """
        Validate password against security policy.
        
        Args:
            password: Password to validate
            
        Returns:
            Dictionary with validation results
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        if not password:
            result['valid'] = False
            result['errors'].append('Password cannot be empty')
            return result
        
        # Check minimum length
        min_length = self.security_config['password_min_length']
        if len(password) < min_length:
            result['valid'] = False
            result['errors'].append(f'Password must be at least {min_length} characters long')
        
        # Check for numbers
        if self.security_config['password_require_numbers']:
            if not any(c.isdigit() for c in password):
                result['valid'] = False
                result['errors'].append('Password must contain at least one number')
        
        # Check for special characters
        if self.security_config['password_require_special']:
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in special_chars for c in password):
                result['valid'] = False
                result['errors'].append('Password must contain at least one special character')
        
        return result
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information."""
        return self.session_manager.get_session(session_id)
    
    def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions."""
        return self.session_manager.cleanup_expired_sessions()

# Global auth service instance
auth_service = AuthService()
