"""
Basic tests for the EDMS application.

Tests core functionality including configuration, database models,
and basic service operations.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Import application modules
from config import get_config
from app.db.session import DatabaseManager
from app.models.user import User
from app.models.document import Document, DocumentStatus
from app.services.auth_service import auth_service
from app.services.storage_service import StorageService
from app.utils.file_utils import get_file_info

class TestConfiguration:
    """Test configuration management."""
    
    def test_config_loading(self):
        """Test that configuration loads successfully."""
        config = get_config()
        
        assert config is not None
        assert 'app' in config
        assert 'database' in config
        assert 'storage' in config
        
        # Check required app settings
        assert config['app']['name'] == 'EDMS'
        assert config['app']['version'] is not None
        assert config['app']['author'] is not None

class TestDatabaseModels:
    """Test database models."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database for testing."""
        temp_dir = tempfile.mkdtemp()
        db_path = Path(temp_dir) / 'test.db'
        
        # Create test database manager
        db_manager = DatabaseManager(f'sqlite:///{db_path}')
        
        # Create tables
        from app.models.base import Base
        db_manager.engine.execute('PRAGMA foreign_keys=ON')
        Base.metadata.create_all(db_manager.engine)
        
        yield db_manager
        
        # Cleanup
        db_manager.close()
        shutil.rmtree(temp_dir)
    
    def test_user_creation(self, temp_db):
        """Test user model creation and password handling."""
        with temp_db.get_session() as session:
            # Create test user
            user = User(
                username='testuser',
                email='<EMAIL>',
                full_name='Test User',
                role='user'
            )
            user.set_password('testpass123')
            
            session.add(user)
            session.commit()
            
            # Verify user was created
            retrieved_user = session.query(User).filter_by(username='testuser').first()
            assert retrieved_user is not None
            assert retrieved_user.email == '<EMAIL>'
            assert retrieved_user.full_name == 'Test User'
            assert retrieved_user.role == 'user'
            
            # Test password verification
            assert retrieved_user.check_password('testpass123')
            assert not retrieved_user.check_password('wrongpassword')
    
    def test_document_creation(self, temp_db):
        """Test document model creation."""
        with temp_db.get_session() as session:
            # Create test user first
            user = User(
                username='docowner',
                email='<EMAIL>',
                full_name='Document Owner',
                role='user'
            )
            user.set_password('password123')
            session.add(user)
            session.flush()  # Get user ID
            
            # Create test document
            document = Document(
                title='Test Document',
                description='A test document for unit testing',
                file_name='test.pdf',
                file_path='documents/test.pdf',
                file_size=1024,
                file_hash='abcd1234',
                mime_type='application/pdf',
                status=DocumentStatus.DRAFT,
                owner_id=user.id,
                organization='Test Org',
                document_date=datetime.now().date()
            )
            
            session.add(document)
            session.commit()
            
            # Verify document was created
            retrieved_doc = session.query(Document).filter_by(title='Test Document').first()
            assert retrieved_doc is not None
            assert retrieved_doc.description == 'A test document for unit testing'
            assert retrieved_doc.file_name == 'test.pdf'
            assert retrieved_doc.status == DocumentStatus.DRAFT
            assert retrieved_doc.owner_id == user.id

class TestAuthService:
    """Test authentication service."""
    
    @pytest.fixture
    def temp_db_with_user(self):
        """Create temporary database with test user."""
        temp_dir = tempfile.mkdtemp()
        db_path = Path(temp_dir) / 'test.db'
        
        # Create test database manager
        db_manager = DatabaseManager(f'sqlite:///{db_path}')
        
        # Create tables
        from app.models.base import Base
        db_manager.engine.execute('PRAGMA foreign_keys=ON')
        Base.metadata.create_all(db_manager.engine)
        
        # Create test user
        with db_manager.get_session() as session:
            user = User(
                username='testauth',
                email='<EMAIL>',
                full_name='Auth Test User',
                role='user'
            )
            user.set_password('authpass123')
            session.add(user)
            session.commit()
        
        # Set database manager for auth service
        auth_service.db_manager = db_manager
        
        yield db_manager
        
        # Cleanup
        db_manager.close()
        shutil.rmtree(temp_dir)
    
    def test_successful_authentication(self, temp_db_with_user):
        """Test successful user authentication."""
        result = auth_service.authenticate('testauth', 'authpass123')
        
        assert result['success'] is True
        assert result['user'] is not None
        assert result['user'].username == 'testauth'
        assert result['session_id'] is not None
    
    def test_failed_authentication(self, temp_db_with_user):
        """Test failed authentication with wrong password."""
        result = auth_service.authenticate('testauth', 'wrongpassword')
        
        assert result['success'] is False
        assert 'error' in result
        assert result['user'] is None
        assert result['session_id'] is None
    
    def test_nonexistent_user_authentication(self, temp_db_with_user):
        """Test authentication with non-existent user."""
        result = auth_service.authenticate('nonexistent', 'password')
        
        assert result['success'] is False
        assert 'error' in result

class TestStorageService:
    """Test storage service."""
    
    @pytest.fixture
    def temp_storage(self):
        """Create temporary storage directory."""
        temp_dir = tempfile.mkdtemp()
        storage_path = Path(temp_dir) / 'storage'
        storage_path.mkdir()
        
        # Create test file
        test_file = Path(temp_dir) / 'test.txt'
        test_file.write_text('This is a test file for storage testing.')
        
        yield temp_dir, test_file, storage_path
        
        # Cleanup
        shutil.rmtree(temp_dir)
    
    def test_file_storage(self, temp_storage):
        """Test file storage functionality."""
        temp_dir, test_file, storage_path = temp_storage
        
        # Create storage service with custom path
        storage_service = StorageService()
        storage_service.documents_path = storage_path
        
        # Store the test file
        result = storage_service.store_file(test_file, 'stored_test.txt')
        
        assert result['success'] is True
        assert 'file_path' in result
        assert 'file_size' in result
        assert 'file_hash' in result
        assert 'mime_type' in result
        
        # Verify file was actually stored
        stored_file = Path(result['file_path'])
        assert stored_file.exists()
        assert stored_file.read_text() == 'This is a test file for storage testing.'

class TestFileUtils:
    """Test file utility functions."""
    
    def test_get_file_info(self):
        """Test file information extraction."""
        # Create temporary test file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write('Test file content')
            temp_file_path = Path(f.name)
        
        try:
            # Get file info
            file_info = get_file_info(temp_file_path)
            
            assert file_info['exists'] is True
            assert file_info['size'] > 0
            assert file_info['size_mb'] > 0
            assert file_info['mime_type'] == 'text/plain'
            assert file_info['extension'] == '.txt'
            assert file_info['modified'] is not None
            
        finally:
            # Cleanup
            temp_file_path.unlink()
    
    def test_get_file_info_nonexistent(self):
        """Test file info for non-existent file."""
        nonexistent_path = Path('/nonexistent/file.txt')
        file_info = get_file_info(nonexistent_path)
        
        assert file_info['exists'] is False
        assert file_info['size'] == 0
        assert file_info['size_mb'] == 0.0

class TestIntegration:
    """Integration tests for combined functionality."""
    
    @pytest.fixture
    def full_setup(self):
        """Create full test environment."""
        temp_dir = tempfile.mkdtemp()
        
        # Setup database
        db_path = Path(temp_dir) / 'test.db'
        db_manager = DatabaseManager(f'sqlite:///{db_path}')
        
        # Create tables
        from app.models.base import Base
        db_manager.engine.execute('PRAGMA foreign_keys=ON')
        Base.metadata.create_all(db_manager.engine)
        
        # Setup storage
        storage_path = Path(temp_dir) / 'storage'
        storage_path.mkdir()
        
        # Create test file
        test_file = Path(temp_dir) / 'integration_test.txt'
        test_file.write_text('Integration test file content')
        
        yield {
            'temp_dir': temp_dir,
            'db_manager': db_manager,
            'storage_path': storage_path,
            'test_file': test_file
        }
        
        # Cleanup
        db_manager.close()
        shutil.rmtree(temp_dir)
    
    def test_full_document_workflow(self, full_setup):
        """Test complete document management workflow."""
        setup = full_setup
        
        with setup['db_manager'].get_session() as session:
            # Create user
            user = User(
                username='workflow_user',
                email='<EMAIL>',
                full_name='Workflow Test User',
                role='user'
            )
            user.set_password('workflow123')
            session.add(user)
            session.flush()
            
            # Store file
            storage_service = StorageService()
            storage_service.documents_path = setup['storage_path']
            
            storage_result = storage_service.store_file(
                setup['test_file'], 
                'workflow_document.txt'
            )
            
            assert storage_result['success'] is True
            
            # Create document record
            document = Document(
                title='Workflow Test Document',
                description='Document for testing complete workflow',
                file_name='workflow_document.txt',
                file_path=storage_result['file_path'],
                file_size=storage_result['file_size'],
                file_hash=storage_result['file_hash'],
                mime_type=storage_result['mime_type'],
                status=DocumentStatus.DRAFT,
                owner_id=user.id,
                organization='Test Organization'
            )
            
            session.add(document)
            session.commit()
            
            # Verify complete workflow
            retrieved_doc = session.query(Document).filter_by(
                title='Workflow Test Document'
            ).first()
            
            assert retrieved_doc is not None
            assert retrieved_doc.owner.username == 'workflow_user'
            assert Path(retrieved_doc.file_path).exists()
            assert retrieved_doc.status == DocumentStatus.DRAFT

if __name__ == '__main__':
    pytest.main([__file__])
