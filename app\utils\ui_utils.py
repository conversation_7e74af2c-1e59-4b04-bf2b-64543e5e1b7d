"""
UI utility functions for the EDMS PyQt5 application.

Provides common UI operations, dialog helpers, styling utilities,
and widget configuration functions.
"""

import logging
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple

from PyQt5.QtCore import Qt, QSize, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QFont, QPalette, QColor
from PyQt5.QtWidgets import (
    QWidget, QMessageBox, QFileDialog, QProgressDialog, QApplication,
    QLabel, QVBoxLayout, QHBoxLayout, QGridLayout, QSplashScreen
)

logger = logging.getLogger(__name__)

def show_message_box(parent: Optional[QWidget], title: str, message: str, 
                    message_type: str = 'information', 
                    buttons: Optional[List[str]] = None) -> str:
    """
    Show a message box with customizable options.
    
    Args:
        parent: Parent widget
        title: Dialog title
        message: Message text
        message_type: Type of message ('information', 'warning', 'error', 'question')
        buttons: List of button labels (default: ['OK'])
        
    Returns:
        Text of clicked button
    """
    if buttons is None:
        buttons = ['OK']
    
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    
    # Set icon based on message type
    icon_map = {
        'information': QMessageBox.Information,
        'warning': QMessageBox.Warning,
        'error': QMessageBox.Critical,
        'question': QMessageBox.Question
    }
    msg_box.setIcon(icon_map.get(message_type, QMessageBox.Information))
    
    # Add custom buttons
    button_objects = []
    for button_text in buttons:
        button = msg_box.addButton(button_text, QMessageBox.ActionRole)
        button_objects.append(button)
    
    # Show dialog and get result
    msg_box.exec_()
    clicked_button = msg_box.clickedButton()
    
    # Find which button was clicked
    for i, button in enumerate(button_objects):
        if button == clicked_button:
            return buttons[i]
    
    return buttons[0] if buttons else 'OK'

def show_error_dialog(parent: Optional[QWidget], title: str, error_message: str, 
                     details: Optional[str] = None):
    """
    Show an error dialog with optional details.
    
    Args:
        parent: Parent widget
        title: Dialog title
        error_message: Main error message
        details: Optional detailed error information
    """
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(error_message)
    msg_box.setIcon(QMessageBox.Critical)
    
    if details:
        msg_box.setDetailedText(details)
    
    msg_box.exec_()

def show_confirmation_dialog(parent: Optional[QWidget], title: str, 
                           message: str, default_yes: bool = False) -> bool:
    """
    Show a yes/no confirmation dialog.
    
    Args:
        parent: Parent widget
        title: Dialog title
        message: Confirmation message
        default_yes: Whether 'Yes' should be the default button
        
    Returns:
        True if user clicked Yes, False otherwise
    """
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setIcon(QMessageBox.Question)
    
    yes_button = msg_box.addButton(QMessageBox.Yes)
    no_button = msg_box.addButton(QMessageBox.No)
    
    if default_yes:
        msg_box.setDefaultButton(yes_button)
    else:
        msg_box.setDefaultButton(no_button)
    
    result = msg_box.exec_()
    return result == QMessageBox.Yes

def select_file_dialog(parent: Optional[QWidget], title: str = "Select File",
                      file_filter: str = "All Files (*)", 
                      initial_dir: Optional[str] = None) -> Optional[str]:
    """
    Show file selection dialog.
    
    Args:
        parent: Parent widget
        title: Dialog title
        file_filter: File filter string
        initial_dir: Initial directory
        
    Returns:
        Selected file path or None if cancelled
    """
    dialog = QFileDialog(parent)
    dialog.setWindowTitle(title)
    dialog.setFileMode(QFileDialog.ExistingFile)
    dialog.setNameFilter(file_filter)
    
    if initial_dir:
        dialog.setDirectory(initial_dir)
    
    if dialog.exec_() == QFileDialog.Accepted:
        selected_files = dialog.selectedFiles()
        return selected_files[0] if selected_files else None
    
    return None

def select_files_dialog(parent: Optional[QWidget], title: str = "Select Files",
                       file_filter: str = "All Files (*)", 
                       initial_dir: Optional[str] = None) -> List[str]:
    """
    Show multiple file selection dialog.
    
    Args:
        parent: Parent widget
        title: Dialog title
        file_filter: File filter string
        initial_dir: Initial directory
        
    Returns:
        List of selected file paths
    """
    dialog = QFileDialog(parent)
    dialog.setWindowTitle(title)
    dialog.setFileMode(QFileDialog.ExistingFiles)
    dialog.setNameFilter(file_filter)
    
    if initial_dir:
        dialog.setDirectory(initial_dir)
    
    if dialog.exec_() == QFileDialog.Accepted:
        return dialog.selectedFiles()
    
    return []

def select_directory_dialog(parent: Optional[QWidget], title: str = "Select Directory",
                           initial_dir: Optional[str] = None) -> Optional[str]:
    """
    Show directory selection dialog.
    
    Args:
        parent: Parent widget
        title: Dialog title
        initial_dir: Initial directory
        
    Returns:
        Selected directory path or None if cancelled
    """
    dialog = QFileDialog(parent)
    dialog.setWindowTitle(title)
    dialog.setFileMode(QFileDialog.Directory)
    dialog.setOption(QFileDialog.ShowDirsOnly, True)
    
    if initial_dir:
        dialog.setDirectory(initial_dir)
    
    if dialog.exec_() == QFileDialog.Accepted:
        selected_dirs = dialog.selectedFiles()
        return selected_dirs[0] if selected_dirs else None
    
    return None

def save_file_dialog(parent: Optional[QWidget], title: str = "Save File",
                    file_filter: str = "All Files (*)", 
                    default_name: str = "", 
                    initial_dir: Optional[str] = None) -> Optional[str]:
    """
    Show save file dialog.
    
    Args:
        parent: Parent widget
        title: Dialog title
        file_filter: File filter string
        default_name: Default filename
        initial_dir: Initial directory
        
    Returns:
        Selected save path or None if cancelled
    """
    dialog = QFileDialog(parent)
    dialog.setWindowTitle(title)
    dialog.setAcceptMode(QFileDialog.AcceptSave)
    dialog.setNameFilter(file_filter)
    
    if default_name:
        dialog.selectFile(default_name)
    
    if initial_dir:
        dialog.setDirectory(initial_dir)
    
    if dialog.exec_() == QFileDialog.Accepted:
        selected_files = dialog.selectedFiles()
        return selected_files[0] if selected_files else None
    
    return None

def create_progress_dialog(parent: Optional[QWidget], title: str, 
                          label_text: str, maximum: int = 100,
                          minimum: int = 0) -> QProgressDialog:
    """
    Create a progress dialog.
    
    Args:
        parent: Parent widget
        title: Dialog title
        label_text: Progress label text
        maximum: Maximum progress value
        minimum: Minimum progress value
        
    Returns:
        QProgressDialog instance
    """
    progress = QProgressDialog(label_text, "Cancel", minimum, maximum, parent)
    progress.setWindowTitle(title)
    progress.setWindowModality(Qt.WindowModal)
    progress.setAutoClose(True)
    progress.setAutoReset(True)
    
    return progress

def load_icon(icon_path: str, size: Optional[QSize] = None) -> QIcon:
    """
    Load icon from file path.
    
    Args:
        icon_path: Path to icon file
        size: Optional icon size
        
    Returns:
        QIcon instance
    """
    icon = QIcon()
    
    if Path(icon_path).exists():
        pixmap = QPixmap(icon_path)
        if size:
            pixmap = pixmap.scaled(size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        icon.addPixmap(pixmap)
    else:
        logger.warning(f"Icon file not found: {icon_path}")
    
    return icon

def set_widget_style(widget: QWidget, style_dict: Dict[str, Any]):
    """
    Apply style properties to widget.
    
    Args:
        widget: Widget to style
        style_dict: Dictionary of style properties
    """
    style_parts = []
    
    for property_name, value in style_dict.items():
        if property_name == 'background-color':
            style_parts.append(f"background-color: {value};")
        elif property_name == 'color':
            style_parts.append(f"color: {value};")
        elif property_name == 'font-size':
            style_parts.append(f"font-size: {value};")
        elif property_name == 'font-weight':
            style_parts.append(f"font-weight: {value};")
        elif property_name == 'border':
            style_parts.append(f"border: {value};")
        elif property_name == 'border-radius':
            style_parts.append(f"border-radius: {value};")
        elif property_name == 'padding':
            style_parts.append(f"padding: {value};")
        elif property_name == 'margin':
            style_parts.append(f"margin: {value};")
        else:
            style_parts.append(f"{property_name}: {value};")
    
    if style_parts:
        widget.setStyleSheet(' '.join(style_parts))

def create_labeled_widget(label_text: str, widget: QWidget, 
                         layout_type: str = 'horizontal') -> QWidget:
    """
    Create a widget with a label.
    
    Args:
        label_text: Label text
        widget: Widget to label
        layout_type: Layout type ('horizontal' or 'vertical')
        
    Returns:
        Container widget with label and widget
    """
    container = QWidget()
    label = QLabel(label_text)
    
    if layout_type == 'horizontal':
        layout = QHBoxLayout()
        layout.addWidget(label)
        layout.addWidget(widget)
    else:
        layout = QVBoxLayout()
        layout.addWidget(label)
        layout.addWidget(widget)
    
    layout.setContentsMargins(0, 0, 0, 0)
    container.setLayout(layout)
    
    return container

def center_widget_on_screen(widget: QWidget):
    """
    Center widget on screen.
    
    Args:
        widget: Widget to center
    """
    screen = QApplication.desktop().screenGeometry()
    widget_geometry = widget.geometry()
    
    x = (screen.width() - widget_geometry.width()) // 2
    y = (screen.height() - widget_geometry.height()) // 2
    
    widget.move(x, y)

def center_widget_on_parent(widget: QWidget, parent: QWidget):
    """
    Center widget on parent widget.
    
    Args:
        widget: Widget to center
        parent: Parent widget
    """
    parent_geometry = parent.geometry()
    widget_geometry = widget.geometry()
    
    x = parent_geometry.x() + (parent_geometry.width() - widget_geometry.width()) // 2
    y = parent_geometry.y() + (parent_geometry.height() - widget_geometry.height()) // 2
    
    widget.move(x, y)

def apply_theme(widget: QWidget, theme_name: str = 'default'):
    """
    Apply theme to widget.
    
    Args:
        widget: Widget to theme
        theme_name: Theme name
    """
    themes = {
        'default': {
            'background-color': '#f0f0f0',
            'color': '#333333',
            'font-family': 'Arial, sans-serif',
            'font-size': '9pt'
        },
        'dark': {
            'background-color': '#2b2b2b',
            'color': '#ffffff',
            'font-family': 'Arial, sans-serif',
            'font-size': '9pt'
        },
        'blue': {
            'background-color': '#e3f2fd',
            'color': '#1565c0',
            'font-family': 'Arial, sans-serif',
            'font-size': '9pt'
        }
    }
    
    theme = themes.get(theme_name, themes['default'])
    set_widget_style(widget, theme)

def create_splash_screen(image_path: str, message: str = "") -> QSplashScreen:
    """
    Create splash screen.
    
    Args:
        image_path: Path to splash image
        message: Optional message to display
        
    Returns:
        QSplashScreen instance
    """
    pixmap = QPixmap(image_path)
    splash = QSplashScreen(pixmap)
    
    if message:
        splash.showMessage(message, Qt.AlignBottom | Qt.AlignCenter, Qt.white)
    
    return splash

def show_tooltip_delayed(widget: QWidget, text: str, delay_ms: int = 500):
    """
    Show tooltip with delay.
    
    Args:
        widget: Widget to show tooltip for
        text: Tooltip text
        delay_ms: Delay in milliseconds
    """
    def show_tooltip():
        widget.setToolTip(text)
    
    QTimer.singleShot(delay_ms, show_tooltip)

def format_file_size_for_display(size_bytes: int) -> str:
    """
    Format file size for display in UI.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"

def truncate_text_for_display(text: str, max_length: int = 50, 
                             suffix: str = "...") -> str:
    """
    Truncate text for display in UI elements.
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add when truncated
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def create_grid_layout_from_widgets(widgets: List[List[QWidget]], 
                                   spacing: int = 5) -> QGridLayout:
    """
    Create grid layout from 2D list of widgets.
    
    Args:
        widgets: 2D list of widgets (rows x columns)
        spacing: Spacing between widgets
        
    Returns:
        QGridLayout instance
    """
    layout = QGridLayout()
    layout.setSpacing(spacing)
    
    for row, widget_row in enumerate(widgets):
        for col, widget in enumerate(widget_row):
            if widget:
                layout.addWidget(widget, row, col)
    
    return layout

def get_system_colors() -> Dict[str, str]:
    """
    Get system color palette.
    
    Returns:
        Dictionary of color names and hex values
    """
    palette = QApplication.palette()
    
    colors = {
        'window': palette.color(QPalette.Window).name(),
        'window_text': palette.color(QPalette.WindowText).name(),
        'base': palette.color(QPalette.Base).name(),
        'text': palette.color(QPalette.Text).name(),
        'button': palette.color(QPalette.Button).name(),
        'button_text': palette.color(QPalette.ButtonText).name(),
        'highlight': palette.color(QPalette.Highlight).name(),
        'highlighted_text': palette.color(QPalette.HighlightedText).name(),
    }
    
    return colors
