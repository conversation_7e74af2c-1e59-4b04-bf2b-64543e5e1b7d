#!/usr/bin/env python3
"""
Main entry point for the EDMS (Electronic Document Management System) application.

This module initializes the application, sets up logging, handles database
initialization, and launches the main GUI.
"""

import sys
import logging
import traceback
from pathlib import Path
from typing import Optional

from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# Import application modules
from app.db.session import DatabaseManager
from app.db.migrations import run_migrations
from app.models.user import User
from app.services.auth_service import auth_service
from app.ui.main_window import MainWindow
from app.utils.ui_utils import show_error_dialog
from config import get_config, setup_directories, setup_logging

logger = logging.getLogger(__name__)

class LoginDialog:
    """Simple login dialog placeholder."""
    
    @staticmethod
    def get_credentials():
        """Get user credentials - placeholder implementation."""
        # TODO: Implement proper login dialog
        # For now, return default admin credentials
        return "admin", "admin123"

class EDMSApplication:
    """Main EDMS application class."""
    
    def __init__(self):
        self.config = get_config()
        self.app: Optional[QApplication] = None
        self.main_window: Optional[MainWindow] = None
        self.db_manager: Optional[DatabaseManager] = None
        
    def initialize(self) -> bool:
        """Initialize the application."""
        try:
            # Setup directories
            setup_directories()
            
            # Setup logging
            setup_logging()
            logger.info("Starting EDMS Application")
            
            # Create Qt application
            self.app = QApplication(sys.argv)
            self.app.setApplicationName(self.config['app']['name'])
            self.app.setApplicationVersion(self.config['app']['version'])
            self.app.setOrganizationName(self.config['app']['author'])
            
            # Set application font
            font = QFont("Arial", 10)
            self.app.setFont(font)
            
            # Show splash screen
            splash = self.create_splash_screen()
            splash.show()
            self.app.processEvents()
            
            # Initialize database
            splash.showMessage("Initializing database...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            if not self.initialize_database():
                splash.close()
                return False
            
            # Run migrations
            splash.showMessage("Running database migrations...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            if not self.run_database_migrations():
                splash.close()
                return False
            
            # Create default admin user if needed
            splash.showMessage("Setting up default user...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            self.create_default_admin_user()
            
            # Initialize main window
            splash.showMessage("Loading main window...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            self.main_window = MainWindow()
            
            # Close splash screen
            splash.finish(self.main_window)
            
            logger.info("Application initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Application initialization failed: {e}")
            logger.error(traceback.format_exc())
            
            if hasattr(self, 'app') and self.app:
                show_error_dialog(
                    None, 
                    "Initialization Error", 
                    f"Failed to initialize application:\n{str(e)}"
                )
            
            return False
    
    def create_splash_screen(self) -> QSplashScreen:
        """Create and return splash screen."""
        # Create a simple splash screen
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.white)
        
        splash = QSplashScreen(pixmap)
        splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        
        # Add some text to the splash screen
        splash.showMessage(
            f"{self.config['app']['name']} v{self.config['app']['version']}\n\nLoading...",
            Qt.AlignCenter,
            Qt.black
        )
        
        return splash
    
    def initialize_database(self) -> bool:
        """Initialize database connection."""
        try:
            self.db_manager = DatabaseManager()
            
            # Test database connection
            with self.db_manager.get_session() as session:
                # Simple test query
                session.execute("SELECT 1")
            
            logger.info("Database initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            show_error_dialog(
                None,
                "Database Error",
                f"Failed to initialize database:\n{str(e)}\n\nPlease check your database configuration."
            )
            return False
    
    def run_database_migrations(self) -> bool:
        """Run database migrations."""
        try:
            run_migrations()
            logger.info("Database migrations completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Database migration failed: {e}")
            show_error_dialog(
                None,
                "Migration Error",
                f"Failed to run database migrations:\n{str(e)}"
            )
            return False
    
    def create_default_admin_user(self):
        """Create default admin user if it doesn't exist."""
        try:
            with self.db_manager.get_session() as session:
                # Check if admin user exists
                admin_user = session.query(User).filter_by(username='admin').first()
                
                if not admin_user:
                    # Create default admin user
                    admin_user = User(
                        username='admin',
                        email='<EMAIL>',
                        full_name='Administrator',
                        role='admin'
                    )
                    admin_user.set_password('admin123')
                    
                    session.add(admin_user)
                    session.commit()
                    
                    logger.info("Default admin user created")
                else:
                    logger.info("Admin user already exists")
                    
        except Exception as e:
            logger.error(f"Failed to create default admin user: {e}")
    
    def authenticate_user(self) -> bool:
        """Authenticate user login."""
        try:
            # Get credentials from login dialog
            username, password = LoginDialog.get_credentials()
            
            if not username or not password:
                return False
            
            # Authenticate user
            auth_result = auth_service.authenticate(username, password)
            
            if auth_result['success']:
                user = auth_result['user']
                session_id = auth_result['session_id']
                
                # Set current user in main window
                self.main_window.set_current_user(user, session_id)
                
                logger.info(f"User {username} authenticated successfully")
                return True
            else:
                show_error_dialog(
                    None,
                    "Authentication Failed",
                    auth_result.get('error', 'Invalid credentials')
                )
                return False
                
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            show_error_dialog(
                None,
                "Authentication Error",
                f"Authentication failed:\n{str(e)}"
            )
            return False
    
    def run(self) -> int:
        """Run the application."""
        if not self.initialize():
            return 1
        
        # Authenticate user
        if not self.authenticate_user():
            return 1
        
        # Show main window
        self.main_window.show()
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Run application event loop
        logger.info("Starting application event loop")
        return self.app.exec_()
    
    def setup_signal_handlers(self):
        """Setup application signal handlers."""
        # Handle user logout
        self.main_window.user_logged_out.connect(self.on_user_logout)
        
        # Handle application quit
        self.app.aboutToQuit.connect(self.on_application_quit)
    
    def on_user_logout(self):
        """Handle user logout."""
        logger.info("User logged out")
        
        # Hide main window
        self.main_window.hide()
        
        # Authenticate new user
        if self.authenticate_user():
            self.main_window.show()
        else:
            self.app.quit()
    
    def on_application_quit(self):
        """Handle application quit."""
        logger.info("Application shutting down")
        
        # Cleanup resources
        if self.db_manager:
            self.db_manager.close()
        
        # Save application state
        # TODO: Implement state saving
        
        logger.info("Application shutdown complete")

def main():
    """Main entry point."""
    try:
        # Create and run application
        app = EDMSApplication()
        return app.run()
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        return 0
        
    except Exception as e:
        print(f"Fatal error: {e}")
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
