"""
Database migration utilities for the EDMS application.

Provides database schema versioning, migration management,
and data seeding capabilities.
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

from sqlalchemy import Column, Integer, String, DateTime, Text, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.db.session import get_engine, get_db_session
from config import get_config

logger = logging.getLogger(__name__)

# Migration tracking table
MigrationBase = declarative_base()

class Migration(MigrationBase):
    """Track applied database migrations."""
    __tablename__ = 'migrations'
    
    id = Column(Integer, primary_key=True)
    version = Column(String(50), unique=True, nullable=False)
    name = Column(String(200), nullable=False)
    applied_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    description = Column(Text, nullable=True)

class MigrationManager:
    """Manage database migrations and schema versioning."""
    
    def __init__(self):
        self.engine = get_engine()
        self.migrations = self._get_available_migrations()
    
    def _get_available_migrations(self) -> List[Dict[str, Any]]:
        """Get list of available migrations in order."""
        return [
            {
                'version': '001',
                'name': 'initial_schema',
                'description': 'Create initial database schema',
                'function': self._migration_001_initial_schema
            },
            {
                'version': '002',
                'name': 'add_indexes',
                'description': 'Add database indexes for performance',
                'function': self._migration_002_add_indexes
            },
            {
                'version': '003',
                'name': 'seed_admin_user',
                'description': 'Create default admin user',
                'function': self._migration_003_seed_admin_user
            }
        ]
    
    def init_migration_table(self):
        """Initialize the migration tracking table."""
        MigrationBase.metadata.create_all(self.engine)
        logger.info("Migration tracking table initialized")
    
    def get_applied_migrations(self) -> List[str]:
        """Get list of applied migration versions."""
        try:
            with get_db_session() as session:
                migrations = session.query(Migration).order_by(Migration.applied_at).all()
                return [m.version for m in migrations]
        except Exception as e:
            logger.warning(f"Could not get applied migrations: {e}")
            return []
    
    def is_migration_applied(self, version: str) -> bool:
        """Check if a migration has been applied."""
        applied = self.get_applied_migrations()
        return version in applied
    
    def apply_migration(self, migration: Dict[str, Any]) -> bool:
        """
        Apply a single migration.
        
        Args:
            migration: Migration dictionary with version, name, description, function
            
        Returns:
            True if successful, False otherwise
        """
        version = migration['version']
        name = migration['name']
        description = migration.get('description', '')
        migration_func = migration['function']
        
        if self.is_migration_applied(version):
            logger.info(f"Migration {version} ({name}) already applied, skipping")
            return True
        
        try:
            logger.info(f"Applying migration {version}: {name}")
            
            # Execute migration function
            migration_func()
            
            # Record migration as applied
            with get_db_session() as session:
                migration_record = Migration(
                    version=version,
                    name=name,
                    description=description
                )
                session.add(migration_record)
            
            logger.info(f"Migration {version} applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply migration {version}: {e}")
            return False
    
    def migrate(self) -> bool:
        """
        Apply all pending migrations.
        
        Returns:
            True if all migrations successful, False otherwise
        """
        logger.info("Starting database migration")
        
        # Initialize migration table if needed
        self.init_migration_table()
        
        # Apply each migration in order
        success = True
        for migration in self.migrations:
            if not self.apply_migration(migration):
                success = False
                break
        
        if success:
            logger.info("All migrations applied successfully")
        else:
            logger.error("Migration process failed")
        
        return success
    
    def rollback_migration(self, version: str) -> bool:
        """
        Rollback a specific migration (if rollback function exists).
        
        Args:
            version: Migration version to rollback
            
        Returns:
            True if successful, False otherwise
        """
        # For now, rollbacks are not implemented
        # In a production system, you would implement rollback functions
        logger.warning(f"Rollback not implemented for migration {version}")
        return False
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status."""
        applied = self.get_applied_migrations()
        pending = []
        
        for migration in self.migrations:
            if migration['version'] not in applied:
                pending.append(migration['version'])
        
        return {
            'applied_migrations': applied,
            'pending_migrations': pending,
            'total_migrations': len(self.migrations),
            'applied_count': len(applied),
            'pending_count': len(pending)
        }
    
    # Migration functions
    def _migration_001_initial_schema(self):
        """Create initial database schema."""
        from app.models.base import Base
        from app.models import user, document, audit
        
        # Create all tables
        Base.metadata.create_all(self.engine)
        logger.info("Initial database schema created")
    
    def _migration_002_add_indexes(self):
        """Add database indexes for performance."""
        # Additional indexes are already defined in the models
        # This migration is a placeholder for future index additions
        logger.info("Database indexes verified")
    
    def _migration_003_seed_admin_user(self):
        """Create default admin user."""
        from app.models.user import User, UserRole
        
        # Check if admin user already exists
        admin_user = User.get_by_username('admin')
        if admin_user:
            logger.info("Admin user already exists, skipping creation")
            return
        
        # Create admin user
        admin_user = User.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',  # Change this in production!
            first_name='System',
            last_name='Administrator',
            role=UserRole.ADMIN
        )
        
        logger.info(f"Default admin user created: {admin_user.username}")

def create_sample_data():
    """Create sample data for testing and demonstration."""
    from app.models.user import User, UserRole
    from app.models.document import Document, DocumentStatus
    from app.models.audit import AuditLog, AuditAction, AuditLevel
    
    logger.info("Creating sample data")
    
    # Create sample users
    users_data = [
        {
            'username': 'john_doe',
            'email': '<EMAIL>',
            'password': 'password123',
            'first_name': 'John',
            'last_name': 'Doe',
            'role': UserRole.USER
        },
        {
            'username': 'jane_smith',
            'email': '<EMAIL>',
            'password': 'password123',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'role': UserRole.USER
        },
        {
            'username': 'viewer_user',
            'email': '<EMAIL>',
            'password': 'password123',
            'first_name': 'View',
            'last_name': 'Only',
            'role': UserRole.VIEWER
        }
    ]
    
    created_users = []
    for user_data in users_data:
        existing_user = User.get_by_username(user_data['username'])
        if not existing_user:
            user = User.create_user(**user_data)
            created_users.append(user)
            logger.info(f"Created sample user: {user.username}")
    
    # Create sample documents (metadata only, no actual files)
    if created_users:
        sample_owner = created_users[0]
        
        documents_data = [
            {
                'title': 'Company Policy Manual',
                'description': 'Official company policies and procedures',
                'file_name': 'policy_manual.pdf',
                'file_path': 'sample/policy_manual.pdf',
                'file_size': 1024000,
                'file_hash': 'abc123def456',
                'mime_type': 'application/pdf',
                'document_type': 'pdf',
                'status': DocumentStatus.APPROVED.value,
                'organization': 'Human Resources',
                'keywords': ['policy', 'manual', 'hr', 'procedures'],
                'file_number': 'HR-001',
                'owner_id': sample_owner.id
            },
            {
                'title': 'Project Proposal Template',
                'description': 'Standard template for project proposals',
                'file_name': 'project_template.docx',
                'file_path': 'sample/project_template.docx',
                'file_size': 512000,
                'file_hash': 'def456ghi789',
                'mime_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'document_type': 'word',
                'status': DocumentStatus.APPROVED.value,
                'organization': 'Project Management',
                'keywords': ['template', 'project', 'proposal'],
                'file_number': 'PM-001',
                'owner_id': sample_owner.id
            }
        ]
        
        for doc_data in documents_data:
            document = Document(**doc_data)
            document.save()
            logger.info(f"Created sample document: {document.title}")
            
            # Create audit log for document creation
            AuditLog.log_document_action(
                action=AuditAction.DOCUMENT_CREATED,
                document_id=document.id,
                user_id=sample_owner.id,
                description=f"Sample document created: {document.title}"
            )
    
    logger.info("Sample data creation completed")

def reset_database_with_sample_data():
    """Reset database and create sample data."""
    from app.db.session import reset_database
    
    logger.info("Resetting database with sample data")
    
    # Reset database
    reset_database()
    
    # Run migrations
    migration_manager = MigrationManager()
    migration_manager.migrate()
    
    # Create sample data
    create_sample_data()
    
    logger.info("Database reset with sample data completed")

def backup_database(backup_path: str) -> bool:
    """
    Create a backup of the database.
    
    Args:
        backup_path: Path where to save the backup
        
    Returns:
        True if successful, False otherwise
    """
    try:
        import shutil
        from pathlib import Path
        
        config = get_config()
        db_url = config['database']['url']
        
        if 'sqlite:///' in db_url:
            # SQLite file backup
            db_file_path = db_url.replace('sqlite:///', '')
            if Path(db_file_path).exists():
                shutil.copy2(db_file_path, backup_path)
                logger.info(f"Database backed up to: {backup_path}")
                return True
            else:
                logger.error(f"Database file not found: {db_file_path}")
                return False
        else:
            logger.error("Database backup only supported for SQLite")
            return False
            
    except Exception as e:
        logger.error(f"Database backup failed: {e}")
        return False

def restore_database(backup_path: str) -> bool:
    """
    Restore database from backup.
    
    Args:
        backup_path: Path to backup file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        import shutil
        from pathlib import Path
        
        config = get_config()
        db_url = config['database']['url']
        
        if 'sqlite:///' in db_url:
            # SQLite file restore
            db_file_path = db_url.replace('sqlite:///', '')
            if Path(backup_path).exists():
                shutil.copy2(backup_path, db_file_path)
                logger.info(f"Database restored from: {backup_path}")
                return True
            else:
                logger.error(f"Backup file not found: {backup_path}")
                return False
        else:
            logger.error("Database restore only supported for SQLite")
            return False
            
    except Exception as e:
        logger.error(f"Database restore failed: {e}")
        return False
