"""
Upload dialog for the EDMS application.

Provides interface for uploading documents with metadata entry,
file validation, and progress tracking.
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any

from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox, QDateEdit,
    QFileDialog, QProgressBar, QGroupBox, QCheckBox, QMessageBox
)

from app.models.document import DocumentStatus
from app.services.storage_service import StorageService
from app.services.ocr_service import ocr_service
from app.utils.ui_utils import show_error_dialog, show_message_box
from app.utils.file_utils import get_file_info
from config import get_config

logger = logging.getLogger(__name__)

class FileUploadWorker(QThread):
    """Worker thread for file upload operations."""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    upload_completed = pyqtSignal(dict)
    upload_failed = pyqtSignal(str)
    
    def __init__(self, file_path: str, metadata: Dict[str, Any]):
        super().__init__()
        self.file_path = Path(file_path)
        self.metadata = metadata
        self.storage_service = StorageService()
    
    def run(self):
        """Run the upload process."""
        try:
            self.status_updated.emit("Validating file...")
            self.progress_updated.emit(10)
            
            # Validate file
            file_info = get_file_info(self.file_path)
            if not file_info['exists']:
                self.upload_failed.emit("File does not exist")
                return
            
            self.status_updated.emit("Storing file...")
            self.progress_updated.emit(30)
            
            # Store file
            storage_result = self.storage_service.store_file(
                self.file_path, 
                self.file_path.name
            )
            
            if not storage_result['success']:
                self.upload_failed.emit(storage_result['error'])
                return
            
            self.progress_updated.emit(60)
            
            # Extract text if OCR is enabled
            extracted_text = ""
            if ocr_service.is_enabled():
                self.status_updated.emit("Extracting text...")
                ocr_result = ocr_service.extract_text_from_file(self.file_path)
                if ocr_result['success']:
                    extracted_text = ocr_result['text']
            
            self.progress_updated.emit(80)
            
            # Create document record
            self.status_updated.emit("Creating document record...")
            
            # TODO: Create document in database
            
            self.progress_updated.emit(100)
            self.status_updated.emit("Upload completed")
            
            result = {
                'file_path': storage_result['file_path'],
                'file_size': storage_result['file_size'],
                'file_hash': storage_result['file_hash'],
                'mime_type': storage_result['mime_type'],
                'extracted_text': extracted_text,
                'metadata': self.metadata
            }
            
            self.upload_completed.emit(result)
            
        except Exception as e:
            logger.error(f"Upload error: {e}")
            self.upload_failed.emit(str(e))

class UploadDialog(QDialog):
    """Dialog for uploading documents."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = get_config()
        self.selected_file_path: Optional[str] = None
        self.upload_worker: Optional[FileUploadWorker] = None
        
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("Upload Document")
        self.setModal(True)
        self.resize(600, 500)
        
        # Main layout
        layout = QVBoxLayout(self)
        
        # File selection section
        self.create_file_selection_section(layout)
        
        # Metadata section
        self.create_metadata_section(layout)
        
        # Options section
        self.create_options_section(layout)
        
        # Progress section
        self.create_progress_section(layout)
        
        # Buttons
        self.create_buttons_section(layout)
    
    def create_file_selection_section(self, parent_layout: QVBoxLayout):
        """Create file selection section."""
        group = QGroupBox("File Selection")
        layout = QVBoxLayout(group)
        
        # File path display
        file_layout = QHBoxLayout()
        
        self.file_path_label = QLabel("No file selected")
        self.file_path_label.setStyleSheet("border: 1px solid #ccc; padding: 5px; background-color: #f9f9f9;")
        
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_file)
        
        file_layout.addWidget(self.file_path_label, 1)
        file_layout.addWidget(self.browse_button)
        
        layout.addLayout(file_layout)
        
        # File info display
        self.file_info_label = QLabel("")
        self.file_info_label.setWordWrap(True)
        layout.addWidget(self.file_info_label)
        
        parent_layout.addWidget(group)
    
    def create_metadata_section(self, parent_layout: QVBoxLayout):
        """Create metadata input section."""
        group = QGroupBox("Document Information")
        layout = QFormLayout(group)
        
        # Title (required)
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("Enter document title...")
        layout.addRow("Title*:", self.title_input)
        
        # Description
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("Enter document description...")
        layout.addRow("Description:", self.description_input)
        
        # Organization
        self.organization_input = QLineEdit()
        self.organization_input.setPlaceholderText("Enter organization/department...")
        layout.addRow("Organization:", self.organization_input)
        
        # File number
        self.file_number_input = QLineEdit()
        self.file_number_input.setPlaceholderText("Enter file/reference number...")
        layout.addRow("File Number:", self.file_number_input)
        
        # Keywords
        self.keywords_input = QLineEdit()
        self.keywords_input.setPlaceholderText("Enter keywords separated by commas...")
        layout.addRow("Keywords:", self.keywords_input)
        
        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems([status.value.title() for status in DocumentStatus])
        layout.addRow("Status:", self.status_combo)
        
        # Document date
        self.document_date = QDateEdit()
        self.document_date.setCalendarPopup(True)
        layout.addRow("Document Date:", self.document_date)
        
        parent_layout.addWidget(group)
    
    def create_options_section(self, parent_layout: QVBoxLayout):
        """Create upload options section."""
        group = QGroupBox("Options")
        layout = QVBoxLayout(group)
        
        # OCR option
        self.ocr_checkbox = QCheckBox("Extract text using OCR")
        self.ocr_checkbox.setChecked(ocr_service.is_enabled())
        self.ocr_checkbox.setEnabled(ocr_service.is_enabled())
        layout.addWidget(self.ocr_checkbox)
        
        # Create thumbnail option
        self.thumbnail_checkbox = QCheckBox("Create thumbnail (for images)")
        self.thumbnail_checkbox.setChecked(True)
        layout.addWidget(self.thumbnail_checkbox)
        
        parent_layout.addWidget(group)
    
    def create_progress_section(self, parent_layout: QVBoxLayout):
        """Create progress tracking section."""
        group = QGroupBox("Upload Progress")
        layout = QVBoxLayout(group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("")
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)
        
        parent_layout.addWidget(group)
    
    def create_buttons_section(self, parent_layout: QVBoxLayout):
        """Create dialog buttons."""
        button_layout = QHBoxLayout()
        
        self.upload_button = QPushButton("Upload")
        self.upload_button.setEnabled(False)
        self.upload_button.clicked.connect(self.start_upload)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.upload_button)
        button_layout.addWidget(self.cancel_button)
        
        parent_layout.addLayout(button_layout)
    
    def setup_connections(self):
        """Setup signal connections."""
        self.title_input.textChanged.connect(self.validate_form)
        self.file_path_label.textChanged.connect(self.validate_form)
    
    def browse_file(self):
        """Open file browser dialog."""
        file_filter = self.create_file_filter()
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Document",
            "",
            file_filter
        )
        
        if file_path:
            self.set_selected_file(file_path)
    
    def create_file_filter(self) -> str:
        """Create file filter string for dialog."""
        allowed_extensions = self.config['storage']['allowed_extensions']
        
        filters = []
        filters.append("All Supported Files (*" + " *".join(allowed_extensions) + ")")
        filters.append("PDF Files (*.pdf)")
        filters.append("Word Documents (*.docx *.doc)")
        filters.append("Excel Files (*.xlsx *.xls)")
        filters.append("Images (*.jpg *.jpeg *.png *.gif *.bmp *.tiff)")
        filters.append("All Files (*)")
        
        return ";;".join(filters)
    
    def set_selected_file(self, file_path: str):
        """Set the selected file and update UI."""
        self.selected_file_path = file_path
        file_name = Path(file_path).name
        
        # Update file path display
        self.file_path_label.setText(file_name)
        
        # Auto-fill title if empty
        if not self.title_input.text():
            title = Path(file_path).stem.replace('_', ' ').replace('-', ' ')
            self.title_input.setText(title)
        
        # Show file info
        self.show_file_info(file_path)
        
        # Validate form
        self.validate_form()
    
    def show_file_info(self, file_path: str):
        """Display file information."""
        try:
            file_info = get_file_info(Path(file_path))
            
            info_text = f"""
            Size: {file_info['size_mb']} MB
            Type: {file_info['mime_type']}
            Modified: {file_info['modified'].strftime('%Y-%m-%d %H:%M') if file_info['modified'] else 'Unknown'}
            """
            
            self.file_info_label.setText(info_text.strip())
            
        except Exception as e:
            self.file_info_label.setText(f"Error reading file info: {e}")
    
    def validate_form(self):
        """Validate form inputs and enable/disable upload button."""
        is_valid = (
            self.selected_file_path is not None and
            self.title_input.text().strip() != ""
        )
        
        self.upload_button.setEnabled(is_valid)
    
    def start_upload(self):
        """Start the upload process."""
        if not self.selected_file_path or not self.title_input.text().strip():
            show_error_dialog(self, "Validation Error", "Please select a file and enter a title.")
            return
        
        # Collect metadata
        metadata = {
            'title': self.title_input.text().strip(),
            'description': self.description_input.toPlainText().strip(),
            'organization': self.organization_input.text().strip(),
            'file_number': self.file_number_input.text().strip(),
            'keywords': [kw.strip() for kw in self.keywords_input.text().split(',') if kw.strip()],
            'status': self.status_combo.currentText().lower().replace(' ', '_'),
            'document_date': self.document_date.date().toPyDate(),
            'extract_text': self.ocr_checkbox.isChecked(),
            'create_thumbnail': self.thumbnail_checkbox.isChecked()
        }
        
        # Disable form during upload
        self.set_form_enabled(False)
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.status_label.setVisible(True)
        
        # Start upload worker
        self.upload_worker = FileUploadWorker(self.selected_file_path, metadata)
        self.upload_worker.progress_updated.connect(self.progress_bar.setValue)
        self.upload_worker.status_updated.connect(self.status_label.setText)
        self.upload_worker.upload_completed.connect(self.on_upload_completed)
        self.upload_worker.upload_failed.connect(self.on_upload_failed)
        self.upload_worker.start()
    
    def set_form_enabled(self, enabled: bool):
        """Enable or disable form inputs."""
        self.browse_button.setEnabled(enabled)
        self.title_input.setEnabled(enabled)
        self.description_input.setEnabled(enabled)
        self.organization_input.setEnabled(enabled)
        self.file_number_input.setEnabled(enabled)
        self.keywords_input.setEnabled(enabled)
        self.status_combo.setEnabled(enabled)
        self.document_date.setEnabled(enabled)
        self.ocr_checkbox.setEnabled(enabled and ocr_service.is_enabled())
        self.thumbnail_checkbox.setEnabled(enabled)
        self.upload_button.setEnabled(enabled and self.selected_file_path is not None)
    
    def on_upload_completed(self, result: Dict[str, Any]):
        """Handle successful upload completion."""
        show_message_box(self, "Success", "Document uploaded successfully!")
        self.accept()
    
    def on_upload_failed(self, error_message: str):
        """Handle upload failure."""
        show_error_dialog(self, "Upload Failed", f"Failed to upload document: {error_message}")
        
        # Re-enable form
        self.set_form_enabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
    
    def closeEvent(self, event):
        """Handle dialog close event."""
        if self.upload_worker and self.upload_worker.isRunning():
            reply = show_message_box(
                self,
                "Upload in Progress",
                "Upload is in progress. Are you sure you want to cancel?",
                "question",
                ["Yes", "No"]
            )
            
            if reply == "Yes":
                self.upload_worker.terminate()
                self.upload_worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
