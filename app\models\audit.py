"""
Audit log model for tracking user activities and system events in the EDMS application.

Provides comprehensive logging of all sensitive operations including
document access, modifications, user actions, and system events.
"""

import logging
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List

from sqlalchemy import Column, String, Integer, DateTime, Text, ForeignKey, JSO<PERSON>
from sqlalchemy.orm import relationship

from app.models.base import BaseModel

logger = logging.getLogger(__name__)

class AuditAction(Enum):
    """Types of actions that can be audited."""
    # Authentication actions
    LOGIN = "login"
    LOGOUT = "logout"
    LOGIN_FAILED = "login_failed"
    PASSWORD_CHANGED = "password_changed"
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_UNLOCKED = "account_unlocked"
    
    # Document actions
    DOCUMENT_CREATED = "document_created"
    DOCUMENT_VIEWED = "document_viewed"
    DOCUMENT_DOWNLOADED = "document_downloaded"
    DOCUMENT_UPDATED = "document_updated"
    DOCUMENT_DELETED = "document_deleted"
    DOCUMENT_RESTORED = "document_restored"
    DOCUMENT_STATUS_CHANGED = "document_status_changed"
    DOCUMENT_VERSION_CREATED = "document_version_created"
    
    # User management actions
    USER_CREATED = "user_created"
    USER_UPDATED = "user_updated"
    USER_DELETED = "user_deleted"
    USER_ROLE_CHANGED = "user_role_changed"
    USER_STATUS_CHANGED = "user_status_changed"
    
    # System actions
    SYSTEM_STARTUP = "system_startup"
    SYSTEM_SHUTDOWN = "system_shutdown"
    DATABASE_BACKUP = "database_backup"
    DATABASE_RESTORE = "database_restore"
    SETTINGS_CHANGED = "settings_changed"
    
    # Search actions
    SEARCH_PERFORMED = "search_performed"
    EXPORT_PERFORMED = "export_performed"

class AuditLevel(Enum):
    """Audit log levels."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AuditLog(BaseModel):
    """
    Audit log model for tracking user activities and system events.
    
    Attributes:
        action: Type of action performed
        level: Log level (info, warning, error, etc.)
        user_id: ID of user who performed the action (nullable for system actions)
        document_id: ID of document involved in the action (nullable)
        target_user_id: ID of target user for user management actions (nullable)
        ip_address: IP address of the client (for network actions)
        user_agent: User agent string (for web/network actions)
        session_id: Session identifier
        description: Human-readable description of the action
        details: Additional details in JSON format
        success: Whether the action was successful
        error_message: Error message if action failed
    """
    __tablename__ = 'audit_logs'
    
    # Action information
    action = Column(String(50), nullable=False, index=True)
    level = Column(String(20), default=AuditLevel.INFO.value, nullable=False, index=True)
    
    # Related entities
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, index=True)
    document_id = Column(Integer, ForeignKey('documents.id'), nullable=True, index=True)
    target_user_id = Column(Integer, ForeignKey('users.id'), nullable=True, index=True)
    
    # Session and network information
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    session_id = Column(String(100), nullable=True, index=True)
    
    # Action details
    description = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    success = Column(String(10), default='true', nullable=False)  # 'true', 'false', 'partial'
    error_message = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="audit_logs")
    document = relationship("Document", back_populates="audit_logs")
    target_user = relationship("User", foreign_keys=[target_user_id])
    
    def __repr__(self) -> str:
        return f"<AuditLog(id={self.id}, action='{self.action}', user_id={self.user_id})>"
    
    @classmethod
    def log_action(cls, action: AuditAction, description: str, 
                   user_id: Optional[int] = None, document_id: Optional[int] = None,
                   target_user_id: Optional[int] = None, level: AuditLevel = AuditLevel.INFO,
                   details: Optional[Dict[str, Any]] = None, success: bool = True,
                   error_message: Optional[str] = None, ip_address: Optional[str] = None,
                   user_agent: Optional[str] = None, session_id: Optional[str] = None) -> 'AuditLog':
        """
        Create a new audit log entry.
        
        Args:
            action: Type of action performed
            description: Human-readable description
            user_id: ID of user who performed the action
            document_id: ID of document involved
            target_user_id: ID of target user (for user management actions)
            level: Log level
            details: Additional details dictionary
            success: Whether the action was successful
            error_message: Error message if action failed
            ip_address: Client IP address
            user_agent: Client user agent
            session_id: Session identifier
            
        Returns:
            Created AuditLog instance
        """
        audit_log = cls(
            action=action.value,
            level=level.value,
            user_id=user_id,
            document_id=document_id,
            target_user_id=target_user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            description=description,
            details=details,
            success='true' if success else 'false',
            error_message=error_message
        )
        audit_log.save()
        
        # Also log to Python logger
        log_level = getattr(logging, level.value.upper())
        logger.log(log_level, f"AUDIT: {description} (Action: {action.value}, User: {user_id})")
        
        return audit_log
    
    @classmethod
    def log_login(cls, user_id: int, success: bool, ip_address: Optional[str] = None,
                  user_agent: Optional[str] = None, error_message: Optional[str] = None) -> 'AuditLog':
        """Log user login attempt."""
        action = AuditAction.LOGIN if success else AuditAction.LOGIN_FAILED
        level = AuditLevel.INFO if success else AuditLevel.WARNING
        description = f"User login {'successful' if success else 'failed'}"
        
        return cls.log_action(
            action=action,
            description=description,
            user_id=user_id,
            level=level,
            success=success,
            error_message=error_message,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    @classmethod
    def log_document_action(cls, action: AuditAction, document_id: int, user_id: int,
                           description: Optional[str] = None, details: Optional[Dict[str, Any]] = None,
                           success: bool = True, error_message: Optional[str] = None) -> 'AuditLog':
        """Log document-related action."""
        if not description:
            description = f"Document {action.value.replace('_', ' ')}"
        
        return cls.log_action(
            action=action,
            description=description,
            user_id=user_id,
            document_id=document_id,
            details=details,
            success=success,
            error_message=error_message
        )
    
    @classmethod
    def log_user_management(cls, action: AuditAction, admin_user_id: int, target_user_id: int,
                           description: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> 'AuditLog':
        """Log user management action."""
        if not description:
            description = f"User {action.value.replace('_', ' ')}"
        
        return cls.log_action(
            action=action,
            description=description,
            user_id=admin_user_id,
            target_user_id=target_user_id,
            details=details
        )
    
    @classmethod
    def log_system_event(cls, action: AuditAction, description: str,
                        details: Optional[Dict[str, Any]] = None, level: AuditLevel = AuditLevel.INFO) -> 'AuditLog':
        """Log system event."""
        return cls.log_action(
            action=action,
            description=description,
            details=details,
            level=level
        )
    
    @classmethod
    def get_user_activity(cls, user_id: int, limit: int = 100, offset: int = 0) -> List['AuditLog']:
        """Get audit logs for a specific user."""
        from app.db.session import get_db_session
        
        with get_db_session() as session:
            return session.query(cls).filter(
                cls.user_id == user_id
            ).order_by(cls.created_at.desc()).offset(offset).limit(limit).all()
    
    @classmethod
    def get_document_activity(cls, document_id: int, limit: int = 100, offset: int = 0) -> List['AuditLog']:
        """Get audit logs for a specific document."""
        from app.db.session import get_db_session
        
        with get_db_session() as session:
            return session.query(cls).filter(
                cls.document_id == document_id
            ).order_by(cls.created_at.desc()).offset(offset).limit(limit).all()
    
    @classmethod
    def get_recent_activity(cls, limit: int = 100, level: Optional[AuditLevel] = None) -> List['AuditLog']:
        """Get recent audit logs."""
        from app.db.session import get_db_session
        
        with get_db_session() as session:
            query = session.query(cls)
            
            if level:
                query = query.filter(cls.level == level.value)
            
            return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_failed_actions(cls, hours: int = 24, limit: int = 100) -> List['AuditLog']:
        """Get failed actions within specified hours."""
        from app.db.session import get_db_session
        from datetime import timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        with get_db_session() as session:
            return session.query(cls).filter(
                cls.success == 'false',
                cls.created_at >= cutoff_time
            ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def cleanup_old_logs(cls, days: int = 90) -> int:
        """
        Clean up audit logs older than specified days.
        
        Args:
            days: Number of days to keep logs
            
        Returns:
            Number of deleted records
        """
        from app.db.session import get_db_session
        from datetime import timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        with get_db_session() as session:
            deleted_count = session.query(cls).filter(
                cls.created_at < cutoff_date
            ).delete()
            
            logger.info(f"Cleaned up {deleted_count} old audit log entries")
            return deleted_count
    
    def to_dict(self, exclude_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """Convert audit log to dictionary with related entity information."""
        result = super().to_dict(exclude_fields=exclude_fields)
        
        # Add related entity information
        if self.user:
            result['user_name'] = self.user.full_name
            result['username'] = self.user.username
        
        if self.document:
            result['document_title'] = self.document.title
            result['document_file_name'] = self.document.file_name
        
        if self.target_user:
            result['target_user_name'] = self.target_user.full_name
            result['target_username'] = self.target_user.username
        
        return result
