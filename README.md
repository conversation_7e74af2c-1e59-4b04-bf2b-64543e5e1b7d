# EDMS - Electronic Document Management System

A comprehensive desktop document management application built with Python 3.10+ and PyQt5, featuring local SQLite storage, OCR capabilities, and an elegant user interface.

## Features

### Core Functionality
- **Document Upload & Storage**: Support for PDF, DOCX, XLSX, and image files
- **Metadata Management**: Comprehensive document information tracking
- **Full-Text Search**: Advanced search with filters and sorting
- **Document Versioning**: Track document changes and history
- **Role-Based Access Control**: Admin, User, and Viewer roles
- **Audit Logging**: Complete activity tracking and security monitoring

### Advanced Features
- **OCR Integration**: Optional text extraction using Tesseract
- **File Deduplication**: Hash-based duplicate detection
- **Thumbnail Generation**: Automatic preview generation for images
- **Database Encryption**: Optional SQLCipher support
- **Backup System**: Automated database and file backups
- **Cross-Platform**: Windows, macOS, and Linux support

## Installation

### Prerequisites
- Python 3.10 or higher
- pip package manager

### Required Dependencies
```bash
pip install -r requirements.txt
```

### Optional Dependencies
For OCR functionality:
```bash
# Install Tesseract OCR
# Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
# macOS: brew install tesseract
# Ubuntu/Debian: sudo apt-get install tesseract-ocr

pip install pytesseract
```

For database encryption:
```bash
pip install sqlcipher3
```

## Quick Start

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd edms
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

4. **Default login credentials**:
   - Username: `admin`
   - Password: `admin123`

## Configuration

The application uses a configuration-driven approach. Key settings can be modified in `config.py`:

### Database Configuration
```python
'database': {
    'url': 'sqlite:///data/edms.db',
    'encryption_enabled': False,  # Set to True for SQLCipher
    'encryption_key': None,       # Set encryption key if enabled
}
```

### Storage Configuration
```python
'storage': {
    'documents_path': Path('data/documents'),
    'backups_path': Path('data/backups'),
    'max_file_size_mb': 100,
    'allowed_extensions': ['.pdf', '.docx', '.xlsx', '.jpg', '.png'],
}
```

### OCR Configuration
```python
'ocr': {
    'enabled': False,             # Set to True to enable OCR
    'tesseract_path': None,       # Path to tesseract executable
    'languages': ['eng'],         # OCR languages
    'confidence_threshold': 60,   # Minimum confidence for text extraction
}
```

## Usage

### Document Upload
1. Click the "Upload" button in the toolbar or use Ctrl+U
2. Select a file using the file browser
3. Fill in document metadata (title is required)
4. Configure OCR and thumbnail options
5. Click "Upload" to process the document

### Document Search
1. Use the search bar in the main window for quick searches
2. Access the "Search" tab for advanced filtering options
3. Filter by status, organization, date range, file type, and owner
4. Export search results to CSV format

### User Management
Administrators can manage users through the application:
- Create new user accounts
- Assign roles (Admin, User, Viewer)
- Reset passwords
- View user activity logs

### Settings Configuration
Access settings through Edit → Preferences:
- **General**: Auto-save intervals, notifications
- **Storage**: File paths, backup settings
- **Security**: Password policies, session timeouts
- **OCR**: Tesseract configuration and testing
- **UI**: Themes, window settings

## Architecture

### Project Structure
```
edms/
├── app/
│   ├── controllers/         # Business logic controllers
│   ├── db/                 # Database session and migrations
│   ├── models/             # SQLAlchemy ORM models
│   ├── services/           # Core business services
│   ├── ui/                 # PyQt5 user interface
│   └── utils/              # Utility functions
├── data/                   # Application data directory
├── resources/              # UI resources and stylesheets
├── tests/                  # Unit and integration tests
├── config.py               # Application configuration
├── main.py                 # Application entry point
└── requirements.txt        # Python dependencies
```

### Key Components

#### Models
- **User**: Authentication and role management
- **Document**: Document metadata and relationships
- **AuditLog**: Activity tracking and security monitoring

#### Services
- **AuthService**: User authentication and session management
- **StorageService**: File storage and retrieval
- **OCRService**: Text extraction from documents
- **AuditService**: Activity logging and reporting

#### UI Components
- **MainWindow**: Primary application interface
- **UploadDialog**: Document upload with metadata entry
- **SearchWidget**: Advanced search and filtering
- **SettingsDialog**: Application configuration

## Development

### Running Tests
```bash
python -m pytest tests/
```

### Code Style
The project follows PEP 8 coding standards. Use tools like `black` and `flake8` for code formatting and linting.

### Database Migrations
Database schema changes are managed through the migration system:
```bash
# Create a new migration
python -c "from app.db.migrations import create_migration; create_migration('description')"

# Run migrations
python -c "from app.db.migrations import run_migrations; run_migrations()"
```

## Security Considerations

- **Password Hashing**: Uses bcrypt for secure password storage
- **Session Management**: Automatic session timeout and cleanup
- **Audit Logging**: Comprehensive activity tracking
- **File Validation**: Strict file type and size validation
- **Database Encryption**: Optional SQLCipher support
- **Role-Based Access**: Granular permission system

## Troubleshooting

### Common Issues

**Database Connection Errors**:
- Ensure the data directory exists and is writable
- Check database file permissions
- Verify SQLite installation

**OCR Not Working**:
- Install Tesseract OCR system package
- Configure correct Tesseract path in settings
- Test OCR configuration in settings dialog

**File Upload Failures**:
- Check file size limits in configuration
- Verify file type is in allowed extensions
- Ensure storage directory is writable

**Performance Issues**:
- Enable database indexing for large document collections
- Configure appropriate pagination limits
- Consider database cleanup for old audit logs

### Logging
Application logs are stored in `data/logs/` directory:
- `edms.log`: General application logs
- `audit.log`: Security and activity logs
- `error.log`: Error-specific logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License. See the LICENSE file for details.

## Support

For support and questions:
- Create an issue on the project repository
- Check the troubleshooting section above
- Review the application logs for error details

## Roadmap

Future enhancements planned:
- Web-based interface option
- Advanced workflow management
- Integration with cloud storage services
- Mobile companion app
- Advanced reporting and analytics
- Multi-language support
