"""
Configuration settings for the Desktop Archive Management Application.

This module contains all configurable settings including database paths,
file storage locations, feature toggles, and UI preferences.
"""

import os
from pathlib import Path
from typing import Dict, Any

# Base application directory
BASE_DIR = Path(__file__).parent.absolute()

# Database Configuration
DATABASE_CONFIG = {
    'url': f'sqlite:///{BASE_DIR}/data/edms.db',
    'echo': False,  # Set to True for SQL query logging
    'pool_pre_ping': True,
    'pool_recycle': 300,
}

# File Storage Configuration
STORAGE_CONFIG = {
    'documents_path': BASE_DIR / 'data' / 'documents',
    'thumbnails_path': BASE_DIR / 'data' / 'thumbnails',
    'backups_path': BASE_DIR / 'data' / 'backups',
    'temp_path': BASE_DIR / 'data' / 'temp',
    'max_file_size_mb': 100,  # Maximum file size in MB
    'allowed_extensions': {
        '.pdf', '.docx', '.xlsx', '.xls', '.doc', '.ppt', '.pptx',
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.txt'
    }
}

# OCR Configuration
OCR_CONFIG = {
    'enabled': False,  # Set to True to enable OCR features
    'tesseract_path': None,  # Path to tesseract executable (auto-detect if None)
    'languages': ['eng'],  # OCR languages
    'confidence_threshold': 60,  # Minimum confidence for OCR text
}

# Security Configuration
SECURITY_CONFIG = {
    'password_min_length': 8,
    'password_require_special': True,
    'password_require_numbers': True,
    'session_timeout_minutes': 480,  # 8 hours
    'max_login_attempts': 5,
    'lockout_duration_minutes': 30,
    'use_encryption': False,  # Set to True to enable SQLCipher
}

# UI Configuration
UI_CONFIG = {
    'theme': 'default',  # Theme name
    'window_size': (1200, 800),  # Default window size
    'window_min_size': (800, 600),  # Minimum window size
    'thumbnail_size': (150, 150),  # Thumbnail dimensions
    'preview_size': (400, 400),  # Preview panel size
    'items_per_page': 50,  # Pagination size
    'auto_save_interval': 300,  # Auto-save interval in seconds
}

# Search Configuration
SEARCH_CONFIG = {
    'max_results': 1000,
    'highlight_matches': True,
    'fuzzy_search': True,
    'search_timeout_seconds': 30,
    'index_content': True,  # Enable full-text search
}

# Audit Configuration
AUDIT_CONFIG = {
    'enabled': True,
    'log_level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
    'max_log_size_mb': 10,
    'backup_count': 5,
    'log_file': BASE_DIR / 'logs' / 'audit.log',
}

# Backup Configuration
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_interval_days': 7,
    'max_backups': 10,
    'compress_backups': True,
    'include_documents': True,
}

# Application Metadata
APP_CONFIG = {
    'name': 'Desktop Archive Management System',
    'version': '1.0.0',
    'author': 'EDMS Development Team',
    'description': 'A comprehensive document management system',
    'support_email': '<EMAIL>',
}

# Development Configuration
DEV_CONFIG = {
    'debug': False,
    'testing': False,
    'log_sql': False,
    'mock_ocr': False,  # Use mock OCR for testing
    'sample_data': True,  # Load sample data on first run
}

def get_config() -> Dict[str, Any]:
    """
    Get the complete configuration dictionary.
    
    Returns:
        Dict containing all configuration settings
    """
    return {
        'database': DATABASE_CONFIG,
        'storage': STORAGE_CONFIG,
        'ocr': OCR_CONFIG,
        'security': SECURITY_CONFIG,
        'ui': UI_CONFIG,
        'search': SEARCH_CONFIG,
        'audit': AUDIT_CONFIG,
        'backup': BACKUP_CONFIG,
        'app': APP_CONFIG,
        'dev': DEV_CONFIG,
    }

def ensure_directories():
    """
    Ensure all required directories exist.
    """
    directories = [
        BASE_DIR / 'data',
        BASE_DIR / 'logs',
        STORAGE_CONFIG['documents_path'],
        STORAGE_CONFIG['thumbnails_path'],
        STORAGE_CONFIG['backups_path'],
        STORAGE_CONFIG['temp_path'],
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def validate_config():
    """
    Validate configuration settings and dependencies.
    
    Raises:
        ValueError: If configuration is invalid
        ImportError: If required dependencies are missing
    """
    # Validate OCR configuration
    if OCR_CONFIG['enabled']:
        try:
            import pytesseract
            if OCR_CONFIG['tesseract_path']:
                pytesseract.pytesseract.tesseract_cmd = OCR_CONFIG['tesseract_path']
        except ImportError:
            raise ImportError("pytesseract is required when OCR is enabled")
    
    # Validate encryption configuration
    if SECURITY_CONFIG['use_encryption']:
        try:
            import sqlcipher3
        except ImportError:
            raise ImportError("sqlcipher3 is required when encryption is enabled")
    
    # Validate file size limits
    if STORAGE_CONFIG['max_file_size_mb'] <= 0:
        raise ValueError("max_file_size_mb must be positive")
    
    # Validate UI configuration
    if UI_CONFIG['window_size'][0] < UI_CONFIG['window_min_size'][0] or \
       UI_CONFIG['window_size'][1] < UI_CONFIG['window_min_size'][1]:
        raise ValueError("window_size must be >= window_min_size")

# Initialize directories on import
ensure_directories()
