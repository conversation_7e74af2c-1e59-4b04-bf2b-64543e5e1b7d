"""
File utility functions for the EDMS application.

Provides common file operations, validation, and metadata extraction
for various file types supported by the system.
"""

import hashlib
import logging
import mimetypes
import os
import shutil
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)

def get_file_info(file_path: Path) -> Dict[str, Any]:
    """
    Get comprehensive file information.
    
    Args:
        file_path: Path to file
        
    Returns:
        Dictionary with file information
    """
    info = {
        'exists': False,
        'size': 0,
        'size_mb': 0.0,
        'extension': '',
        'mime_type': '',
        'created': None,
        'modified': None,
        'hash': None,
        'is_readable': False,
        'is_writable': False
    }
    
    try:
        if not file_path.exists():
            return info
        
        stat = file_path.stat()
        
        info.update({
            'exists': True,
            'size': stat.st_size,
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'extension': file_path.suffix.lower(),
            'created': datetime.fromtimestamp(stat.st_ctime),
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'is_readable': os.access(file_path, os.R_OK),
            'is_writable': os.access(file_path, os.W_OK)
        })
        
        # Get MIME type
        mime_type, _ = mimetypes.guess_type(str(file_path))
        info['mime_type'] = mime_type or 'application/octet-stream'
        
        # Calculate file hash
        info['hash'] = calculate_file_hash(file_path)
        
    except Exception as e:
        logger.error(f"Error getting file info for {file_path}: {e}")
    
    return info

def calculate_file_hash(file_path: Path, algorithm: str = 'sha256') -> Optional[str]:
    """
    Calculate hash of file content.
    
    Args:
        file_path: Path to file
        algorithm: Hash algorithm (md5, sha1, sha256, sha512)
        
    Returns:
        Hexadecimal hash string or None if error
    """
    try:
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(8192), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
        
    except Exception as e:
        logger.error(f"Error calculating {algorithm} hash for {file_path}: {e}")
        return None

def validate_file_type(file_path: Path, allowed_extensions: List[str]) -> Dict[str, Any]:
    """
    Validate file type against allowed extensions.
    
    Args:
        file_path: Path to file
        allowed_extensions: List of allowed extensions (with dots)
        
    Returns:
        Dictionary with validation results
    """
    result = {
        'valid': False,
        'extension': '',
        'mime_type': '',
        'error': None
    }
    
    try:
        if not file_path.exists():
            result['error'] = 'File does not exist'
            return result
        
        extension = file_path.suffix.lower()
        result['extension'] = extension
        
        # Get MIME type
        mime_type, _ = mimetypes.guess_type(str(file_path))
        result['mime_type'] = mime_type or 'application/octet-stream'
        
        # Check if extension is allowed
        if extension in allowed_extensions:
            result['valid'] = True
        else:
            result['error'] = f'File type "{extension}" is not allowed'
        
    except Exception as e:
        result['error'] = f'File validation error: {str(e)}'
        logger.error(f"File type validation error for {file_path}: {e}")
    
    return result

def safe_filename(filename: str, max_length: int = 255) -> str:
    """
    Create a safe filename by removing/replacing invalid characters.
    
    Args:
        filename: Original filename
        max_length: Maximum filename length
        
    Returns:
        Safe filename
    """
    # Characters to remove or replace
    invalid_chars = '<>:"/\\|?*'
    
    # Replace invalid characters with underscores
    safe_name = filename
    for char in invalid_chars:
        safe_name = safe_name.replace(char, '_')
    
    # Remove control characters
    safe_name = ''.join(char for char in safe_name if ord(char) >= 32)
    
    # Trim whitespace and dots from ends
    safe_name = safe_name.strip(' .')
    
    # Ensure it's not empty
    if not safe_name:
        safe_name = 'unnamed_file'
    
    # Truncate if too long, preserving extension
    if len(safe_name) > max_length:
        name_part = Path(safe_name).stem
        ext_part = Path(safe_name).suffix
        
        # Calculate available space for name
        available_length = max_length - len(ext_part)
        if available_length > 0:
            safe_name = name_part[:available_length] + ext_part
        else:
            safe_name = safe_name[:max_length]
    
    return safe_name

def copy_file_safely(source: Path, destination: Path, 
                    verify_hash: bool = True) -> Dict[str, Any]:
    """
    Copy file with verification and error handling.
    
    Args:
        source: Source file path
        destination: Destination file path
        verify_hash: Whether to verify file integrity after copy
        
    Returns:
        Dictionary with copy results
    """
    result = {
        'success': False,
        'source_hash': None,
        'destination_hash': None,
        'bytes_copied': 0,
        'error': None
    }
    
    try:
        if not source.exists():
            result['error'] = 'Source file does not exist'
            return result
        
        # Calculate source hash if verification requested
        if verify_hash:
            result['source_hash'] = calculate_file_hash(source)
        
        # Ensure destination directory exists
        destination.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy file
        shutil.copy2(source, destination)
        
        # Get copied file size
        if destination.exists():
            result['bytes_copied'] = destination.stat().st_size
            
            # Verify integrity if requested
            if verify_hash:
                result['destination_hash'] = calculate_file_hash(destination)
                
                if result['source_hash'] != result['destination_hash']:
                    # Remove corrupted copy
                    destination.unlink()
                    result['error'] = 'File corruption detected during copy'
                    return result
            
            result['success'] = True
        else:
            result['error'] = 'File copy failed - destination not created'
        
    except Exception as e:
        result['error'] = f'Copy error: {str(e)}'
        logger.error(f"File copy error from {source} to {destination}: {e}")
    
    return result

def move_file_safely(source: Path, destination: Path) -> Dict[str, Any]:
    """
    Move file with error handling.
    
    Args:
        source: Source file path
        destination: Destination file path
        
    Returns:
        Dictionary with move results
    """
    result = {
        'success': False,
        'error': None
    }
    
    try:
        if not source.exists():
            result['error'] = 'Source file does not exist'
            return result
        
        # Ensure destination directory exists
        destination.parent.mkdir(parents=True, exist_ok=True)
        
        # Move file
        shutil.move(str(source), str(destination))
        
        # Verify move
        if destination.exists() and not source.exists():
            result['success'] = True
        else:
            result['error'] = 'File move verification failed'
        
    except Exception as e:
        result['error'] = f'Move error: {str(e)}'
        logger.error(f"File move error from {source} to {destination}: {e}")
    
    return result

def delete_file_safely(file_path: Path, to_trash: bool = False) -> Dict[str, Any]:
    """
    Delete file with error handling.
    
    Args:
        file_path: Path to file to delete
        to_trash: Whether to move to trash instead of permanent delete
        
    Returns:
        Dictionary with deletion results
    """
    result = {
        'success': False,
        'error': None
    }
    
    try:
        if not file_path.exists():
            result['error'] = 'File does not exist'
            return result
        
        if to_trash:
            # Try to use system trash if available
            try:
                import send2trash
                send2trash.send2trash(str(file_path))
                result['success'] = True
            except ImportError:
                # Fallback to regular deletion
                file_path.unlink()
                result['success'] = True
        else:
            # Permanent deletion
            file_path.unlink()
            result['success'] = True
        
    except Exception as e:
        result['error'] = f'Delete error: {str(e)}'
        logger.error(f"File delete error for {file_path}: {e}")
    
    return result

def create_temp_file(suffix: str = '', prefix: str = 'edms_', 
                    content: Optional[bytes] = None) -> Path:
    """
    Create temporary file.
    
    Args:
        suffix: File suffix/extension
        prefix: File prefix
        content: Optional content to write to file
        
    Returns:
        Path to temporary file
    """
    try:
        with tempfile.NamedTemporaryFile(
            suffix=suffix, prefix=prefix, delete=False
        ) as temp_file:
            if content:
                temp_file.write(content)
            temp_path = Path(temp_file.name)
        
        return temp_path
        
    except Exception as e:
        logger.error(f"Error creating temporary file: {e}")
        raise

def cleanup_temp_files(temp_dir: Optional[Path] = None, 
                      older_than_hours: int = 24) -> int:
    """
    Clean up old temporary files.
    
    Args:
        temp_dir: Directory to clean (default: system temp)
        older_than_hours: Delete files older than this many hours
        
    Returns:
        Number of files deleted
    """
    if temp_dir is None:
        temp_dir = Path(tempfile.gettempdir())
    
    deleted_count = 0
    cutoff_time = datetime.now().timestamp() - (older_than_hours * 3600)
    
    try:
        # Look for EDMS temporary files
        for file_path in temp_dir.glob('edms_*'):
            try:
                if file_path.is_file():
                    file_time = file_path.stat().st_mtime
                    if file_time < cutoff_time:
                        file_path.unlink()
                        deleted_count += 1
            except Exception as e:
                logger.warning(f"Could not delete temp file {file_path}: {e}")
        
    except Exception as e:
        logger.error(f"Error cleaning temp files: {e}")
    
    return deleted_count

def get_directory_size(directory: Path) -> Dict[str, Any]:
    """
    Calculate total size of directory and its contents.
    
    Args:
        directory: Directory path
        
    Returns:
        Dictionary with size information
    """
    result = {
        'total_size': 0,
        'total_size_mb': 0.0,
        'file_count': 0,
        'directory_count': 0,
        'error': None
    }
    
    try:
        if not directory.exists() or not directory.is_dir():
            result['error'] = 'Directory does not exist'
            return result
        
        for item in directory.rglob('*'):
            try:
                if item.is_file():
                    result['total_size'] += item.stat().st_size
                    result['file_count'] += 1
                elif item.is_dir():
                    result['directory_count'] += 1
            except Exception as e:
                logger.warning(f"Could not process {item}: {e}")
        
        result['total_size_mb'] = round(result['total_size'] / (1024 * 1024), 2)
        
    except Exception as e:
        result['error'] = f'Directory size calculation error: {str(e)}'
        logger.error(f"Error calculating directory size for {directory}: {e}")
    
    return result

def find_duplicate_files(directory: Path, 
                        by_hash: bool = True) -> Dict[str, List[Path]]:
    """
    Find duplicate files in directory.
    
    Args:
        directory: Directory to search
        by_hash: Whether to compare by hash (True) or size only (False)
        
    Returns:
        Dictionary mapping hash/size to list of duplicate file paths
    """
    duplicates = {}
    
    try:
        if not directory.exists() or not directory.is_dir():
            return duplicates
        
        # Group files by hash or size
        file_groups = {}
        
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                try:
                    if by_hash:
                        key = calculate_file_hash(file_path)
                    else:
                        key = str(file_path.stat().st_size)
                    
                    if key:
                        if key not in file_groups:
                            file_groups[key] = []
                        file_groups[key].append(file_path)
                        
                except Exception as e:
                    logger.warning(f"Could not process file {file_path}: {e}")
        
        # Find groups with multiple files (duplicates)
        for key, files in file_groups.items():
            if len(files) > 1:
                duplicates[key] = files
        
    except Exception as e:
        logger.error(f"Error finding duplicates in {directory}: {e}")
    
    return duplicates

def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"

def is_text_file(file_path: Path, sample_size: int = 8192) -> bool:
    """
    Check if file appears to be a text file.
    
    Args:
        file_path: Path to file
        sample_size: Number of bytes to sample
        
    Returns:
        True if file appears to be text, False otherwise
    """
    try:
        with open(file_path, 'rb') as f:
            sample = f.read(sample_size)
        
        # Check for null bytes (common in binary files)
        if b'\x00' in sample:
            return False
        
        # Try to decode as UTF-8
        try:
            sample.decode('utf-8')
            return True
        except UnicodeDecodeError:
            pass
        
        # Try other common encodings
        encodings = ['latin-1', 'cp1252', 'iso-8859-1']
        for encoding in encodings:
            try:
                sample.decode(encoding)
                return True
            except UnicodeDecodeError:
                continue
        
        return False
        
    except Exception as e:
        logger.error(f"Error checking if {file_path} is text file: {e}")
        return False
