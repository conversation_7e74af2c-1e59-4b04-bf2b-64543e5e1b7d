"""
Main window for the EDMS application.

Provides the primary user interface with document management,
search functionality, and navigation between different views.
"""

import logging
from typing import Optional, List, Dict, Any

from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QMenu, QAction, QStatusBar, QToolBar, QTabWidget,
    QLabel, QPushButton, QLineEdit, QComboBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QFrame, QMessageBox, QProgressBar
)

from app.models.user import User
from app.services.auth_service import auth_service
from app.utils.ui_utils import show_message_box, show_error_dialog
from config import get_config

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    """Main application window."""
    
    # Signals
    user_logged_out = pyqtSignal()
    document_selected = pyqtSignal(int)  # document_id
    
    def __init__(self):
        super().__init__()
        self.config = get_config()
        self.current_user: Optional[User] = None
        self.session_id: Optional[str] = None
        
        # Initialize UI
        self.init_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.apply_styles()
        
        # Setup timers
        self.setup_timers()
        
        logger.info("Main window initialized")
    
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle(self.config['app']['name'])
        
        # Set window size and position
        window_size = self.config['ui']['window_size']
        min_size = self.config['ui']['window_min_size']
        
        self.resize(*window_size)
        self.setMinimumSize(*min_size)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create header section
        self.create_header_section(main_layout)
        
        # Create main content area
        self.create_main_content(main_layout)
    
    def create_header_section(self, parent_layout: QVBoxLayout):
        """Create the header section with search and filters."""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setMaximumHeight(80)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Search section
        search_label = QLabel("Search:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search documents...")
        self.search_input.returnPressed.connect(self.perform_search)
        
        search_button = QPushButton("Search")
        search_button.clicked.connect(self.perform_search)
        
        # Filter section
        filter_label = QLabel("Filter:")
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All", "Draft", "Under Review", "Approved", "Archived"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        
        self.org_filter = QComboBox()
        self.org_filter.addItem("All Organizations")
        self.org_filter.currentTextChanged.connect(self.apply_filters)
        
        # Add widgets to header layout
        header_layout.addWidget(search_label)
        header_layout.addWidget(self.search_input, 2)
        header_layout.addWidget(search_button)
        header_layout.addWidget(QFrame())  # Spacer
        header_layout.addWidget(filter_label)
        header_layout.addWidget(self.status_filter)
        header_layout.addWidget(self.org_filter)
        header_layout.addStretch()
        
        parent_layout.addWidget(header_frame)
    
    def create_main_content(self, parent_layout: QVBoxLayout):
        """Create the main content area with tabs."""
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Documents tab
        self.documents_tab = self.create_documents_tab()
        self.tab_widget.addTab(self.documents_tab, "Documents")
        
        # Dashboard tab
        self.dashboard_tab = self.create_dashboard_tab()
        self.tab_widget.addTab(self.dashboard_tab, "Dashboard")
        
        # Reports tab (admin only)
        self.reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(self.reports_tab, "Reports")
        
        parent_layout.addWidget(self.tab_widget)
    
    def create_documents_tab(self) -> QWidget:
        """Create the documents management tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create splitter for documents list and preview
        splitter = QSplitter(Qt.Horizontal)
        
        # Documents table
        self.documents_table = QTableWidget()
        self.setup_documents_table()
        splitter.addWidget(self.documents_table)
        
        # Document preview panel
        preview_panel = self.create_preview_panel()
        splitter.addWidget(preview_panel)
        
        # Set splitter proportions
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        
        return tab
    
    def setup_documents_table(self):
        """Setup the documents table widget."""
        # Set columns
        columns = ["Title", "Organization", "Status", "Owner", "Modified", "Size"]
        self.documents_table.setColumnCount(len(columns))
        self.documents_table.setHorizontalHeaderLabels(columns)
        
        # Configure table
        self.documents_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.documents_table.setAlternatingRowColors(True)
        self.documents_table.setSortingEnabled(True)
        
        # Set column widths
        header = self.documents_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Title
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Organization
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Owner
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Modified
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Size
        
        # Connect selection signal
        self.documents_table.itemSelectionChanged.connect(self.on_document_selected)
    
    def create_preview_panel(self) -> QWidget:
        """Create the document preview panel."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMinimumWidth(300)
        
        layout = QVBoxLayout(panel)
        
        # Preview header
        header_label = QLabel("Document Preview")
        header_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(header_label)
        
        # Document info
        self.preview_info = QLabel("Select a document to view details")
        self.preview_info.setWordWrap(True)
        self.preview_info.setAlignment(Qt.AlignTop)
        layout.addWidget(self.preview_info)
        
        # Thumbnail
        self.preview_thumbnail = QLabel()
        self.preview_thumbnail.setAlignment(Qt.AlignCenter)
        self.preview_thumbnail.setMinimumHeight(200)
        self.preview_thumbnail.setStyleSheet("border: 1px solid #ccc; background-color: #f9f9f9;")
        layout.addWidget(self.preview_thumbnail)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.open_button = QPushButton("Open")
        self.open_button.setEnabled(False)
        self.open_button.clicked.connect(self.open_selected_document)
        
        self.download_button = QPushButton("Download")
        self.download_button.setEnabled(False)
        self.download_button.clicked.connect(self.download_selected_document)
        
        button_layout.addWidget(self.open_button)
        button_layout.addWidget(self.download_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        return panel
    
    def create_dashboard_tab(self) -> QWidget:
        """Create the dashboard tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Dashboard title
        title_label = QLabel("Dashboard")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # Statistics cards
        stats_layout = QHBoxLayout()
        
        # Total documents card
        total_docs_card = self.create_stat_card("Total Documents", "0")
        stats_layout.addWidget(total_docs_card)
        
        # Recent uploads card
        recent_uploads_card = self.create_stat_card("Recent Uploads", "0")
        stats_layout.addWidget(recent_uploads_card)
        
        # Pending reviews card
        pending_reviews_card = self.create_stat_card("Pending Reviews", "0")
        stats_layout.addWidget(pending_reviews_card)
        
        # Storage used card
        storage_card = self.create_stat_card("Storage Used", "0 MB")
        stats_layout.addWidget(storage_card)
        
        layout.addLayout(stats_layout)
        
        # Recent activity
        activity_label = QLabel("Recent Activity")
        activity_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(activity_label)
        
        self.activity_table = QTableWidget()
        self.activity_table.setColumnCount(4)
        self.activity_table.setHorizontalHeaderLabels(["Time", "User", "Action", "Document"])
        layout.addWidget(self.activity_table)
        
        layout.addStretch()
        
        return tab
    
    def create_stat_card(self, title: str, value: str) -> QFrame:
        """Create a statistics card widget."""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(card)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #6c757d;")
        
        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        return card
    
    def create_reports_tab(self) -> QWidget:
        """Create the reports tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Reports title
        title_label = QLabel("Reports")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # Report buttons
        button_layout = QHBoxLayout()
        
        activity_report_btn = QPushButton("Activity Report")
        activity_report_btn.clicked.connect(self.generate_activity_report)
        
        storage_report_btn = QPushButton("Storage Report")
        storage_report_btn.clicked.connect(self.generate_storage_report)
        
        user_report_btn = QPushButton("User Report")
        user_report_btn.clicked.connect(self.generate_user_report)
        
        button_layout.addWidget(activity_report_btn)
        button_layout.addWidget(storage_report_btn)
        button_layout.addWidget(user_report_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        return tab

    def setup_menu_bar(self):
        """Setup the application menu bar."""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        upload_action = QAction('Upload Document', self)
        upload_action.setShortcut('Ctrl+U')
        upload_action.triggered.connect(self.upload_document)
        file_menu.addAction(upload_action)

        file_menu.addSeparator()

        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Edit menu
        edit_menu = menubar.addMenu('Edit')

        preferences_action = QAction('Preferences', self)
        preferences_action.triggered.connect(self.show_preferences)
        edit_menu.addAction(preferences_action)

        # View menu
        view_menu = menubar.addMenu('View')

        refresh_action = QAction('Refresh', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
        view_menu.addAction(refresh_action)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        backup_action = QAction('Backup Database', self)
        backup_action.triggered.connect(self.backup_database)
        tools_menu.addAction(backup_action)

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """Setup the application toolbar."""
        toolbar = self.addToolBar('Main')
        toolbar.setMovable(False)

        # Upload button
        upload_action = QAction('Upload', self)
        upload_action.setToolTip('Upload new document')
        upload_action.triggered.connect(self.upload_document)
        toolbar.addAction(upload_action)

        toolbar.addSeparator()

        # Refresh button
        refresh_action = QAction('Refresh', self)
        refresh_action.setToolTip('Refresh document list')
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # User info
        if self.current_user:
            user_label = QLabel(f"Welcome, {self.current_user.full_name}")
            toolbar.addWidget(user_label)

        toolbar.addWidget(QLabel())  # Spacer

        # Logout button
        logout_action = QAction('Logout', self)
        logout_action.setToolTip('Logout from application')
        logout_action.triggered.connect(self.logout)
        toolbar.addAction(logout_action)

    def setup_status_bar(self):
        """Setup the application status bar."""
        self.status_bar = self.statusBar()

        # Status message
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # Connection status
        self.connection_label = QLabel("Connected")
        self.connection_label.setStyleSheet("color: green;")
        self.status_bar.addPermanentWidget(self.connection_label)

    def setup_timers(self):
        """Setup application timers."""
        # Session cleanup timer
        self.session_timer = QTimer()
        self.session_timer.timeout.connect(self.cleanup_expired_sessions)
        self.session_timer.start(300000)  # 5 minutes

        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh)
        self.refresh_timer.start(60000)  # 1 minute

    def apply_styles(self):
        """Apply application styles."""
        theme = self.config['ui']['theme']

        if theme == 'default':
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #f0f0f0;
                }
                QTabWidget::pane {
                    border: 1px solid #c0c0c0;
                    background-color: white;
                }
                QTabBar::tab {
                    background-color: #e0e0e0;
                    padding: 8px 16px;
                    margin-right: 2px;
                }
                QTabBar::tab:selected {
                    background-color: white;
                    border-bottom: 2px solid #007acc;
                }
                QTableWidget {
                    gridline-color: #d0d0d0;
                    background-color: white;
                }
                QTableWidget::item:selected {
                    background-color: #007acc;
                    color: white;
                }
                QPushButton {
                    background-color: #007acc;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #005a9e;
                }
                QPushButton:disabled {
                    background-color: #cccccc;
                    color: #666666;
                }
            """)

    def set_current_user(self, user: User, session_id: str):
        """Set the current user and session."""
        self.current_user = user
        self.session_id = session_id

        # Update UI based on user role
        self.update_ui_for_user()

        # Load initial data
        self.refresh_data()

    def update_ui_for_user(self):
        """Update UI elements based on current user role."""
        if not self.current_user:
            return

        # Hide/show tabs based on role
        if not self.current_user.is_admin:
            # Hide reports tab for non-admin users
            reports_index = self.tab_widget.indexOf(self.reports_tab)
            if reports_index >= 0:
                self.tab_widget.setTabEnabled(reports_index, False)

        # Update toolbar
        self.setup_toolbar()

    # Event handlers
    def perform_search(self):
        """Perform document search."""
        query = self.search_input.text().strip()
        if not query:
            self.refresh_data()
            return

        self.set_status("Searching documents...")
        # TODO: Implement search functionality
        self.set_status("Search completed")

    def apply_filters(self):
        """Apply filters to document list."""
        # TODO: Implement filtering functionality
        pass

    def on_document_selected(self):
        """Handle document selection in table."""
        selected_items = self.documents_table.selectedItems()
        if not selected_items:
            self.clear_preview()
            return

        # Get selected row
        row = selected_items[0].row()
        # TODO: Get document ID and load preview

        self.open_button.setEnabled(True)
        self.download_button.setEnabled(True)

    def clear_preview(self):
        """Clear the document preview panel."""
        self.preview_info.setText("Select a document to view details")
        self.preview_thumbnail.clear()
        self.open_button.setEnabled(False)
        self.download_button.setEnabled(False)

    # Action handlers
    def upload_document(self):
        """Handle document upload action."""
        from app.ui.upload_dialog import UploadDialog

        dialog = UploadDialog(self)
        if dialog.exec_() == dialog.Accepted:
            self.refresh_data()

    def open_selected_document(self):
        """Open the selected document."""
        # TODO: Implement document opening
        show_message_box(self, "Info", "Document opening not yet implemented")

    def download_selected_document(self):
        """Download the selected document."""
        # TODO: Implement document download
        show_message_box(self, "Info", "Document download not yet implemented")

    def refresh_data(self):
        """Refresh all data in the interface."""
        self.set_status("Refreshing data...")

        # TODO: Load documents from database
        # TODO: Update dashboard statistics
        # TODO: Load recent activity

        self.set_status("Data refreshed")

    def auto_refresh(self):
        """Auto-refresh data periodically."""
        if self.current_user:
            # TODO: Check for updates and refresh if needed
            pass

    def cleanup_expired_sessions(self):
        """Clean up expired sessions."""
        if self.session_id:
            auth_service.cleanup_expired_sessions()

    def logout(self):
        """Handle user logout."""
        if self.session_id:
            auth_service.logout(self.session_id)

        self.current_user = None
        self.session_id = None
        self.user_logged_out.emit()

    def show_preferences(self):
        """Show preferences dialog."""
        from app.ui.settings_dialog import SettingsDialog

        dialog = SettingsDialog(self)
        dialog.exec_()

    def backup_database(self):
        """Handle database backup."""
        # TODO: Implement database backup
        show_message_box(self, "Info", "Database backup not yet implemented")

    def show_about(self):
        """Show about dialog."""
        about_text = f"""
        {self.config['app']['name']} v{self.config['app']['version']}

        {self.config['app']['description']}

        Author: {self.config['app']['author']}
        """

        show_message_box(self, "About", about_text)

    # Report generation methods
    def generate_activity_report(self):
        """Generate activity report."""
        # TODO: Implement activity report generation
        show_message_box(self, "Info", "Activity report generation not yet implemented")

    def generate_storage_report(self):
        """Generate storage report."""
        # TODO: Implement storage report generation
        show_message_box(self, "Info", "Storage report generation not yet implemented")

    def generate_user_report(self):
        """Generate user report."""
        # TODO: Implement user report generation
        show_message_box(self, "Info", "User report generation not yet implemented")

    # Utility methods
    def set_status(self, message: str, timeout: int = 5000):
        """Set status bar message."""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("Ready"))

    def show_progress(self, show: bool = True):
        """Show or hide progress bar."""
        self.progress_bar.setVisible(show)

    def set_progress(self, value: int):
        """Set progress bar value."""
        self.progress_bar.setValue(value)

    def closeEvent(self, event):
        """Handle window close event."""
        if self.current_user:
            reply = show_message_box(
                self,
                "Confirm Exit",
                "Are you sure you want to exit?",
                "question",
                ["Yes", "No"]
            )

            if reply == "Yes":
                self.logout()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
