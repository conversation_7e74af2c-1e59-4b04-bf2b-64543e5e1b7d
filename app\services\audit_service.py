"""
Audit service for comprehensive logging and monitoring in the EDMS application.

Provides centralized audit logging, activity monitoring, and security event tracking
with configurable log levels and automatic cleanup.
"""

import logging
import os
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
from typing import Optional, Dict, Any, List

from app.models.audit import AuditLog, AuditAction, AuditLevel
from config import get_config

class AuditService:
    """Service for managing audit logs and security monitoring."""
    
    def __init__(self):
        self.config = get_config()
        self.audit_config = self.config['audit']
        self.logger = self._setup_audit_logger()
    
    def _setup_audit_logger(self) -> logging.Logger:
        """Setup dedicated audit logger with file rotation."""
        logger = logging.getLogger('audit')
        logger.setLevel(getattr(logging, self.audit_config['log_level']))
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        if self.audit_config['enabled']:
            # Ensure log directory exists
            log_file = self.audit_config['log_file']
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Setup rotating file handler
            max_bytes = self.audit_config['max_log_size_mb'] * 1024 * 1024
            handler = RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=self.audit_config['backup_count']
            )
            
            # Set formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def log_action(self, action: AuditAction, description: str,
                   user_id: Optional[int] = None, document_id: Optional[int] = None,
                   target_user_id: Optional[int] = None, level: AuditLevel = AuditLevel.INFO,
                   details: Optional[Dict[str, Any]] = None, success: bool = True,
                   error_message: Optional[str] = None, ip_address: Optional[str] = None,
                   user_agent: Optional[str] = None, session_id: Optional[str] = None) -> Optional[AuditLog]:
        """
        Log an audit action.
        
        Args:
            action: Type of action performed
            description: Human-readable description
            user_id: ID of user who performed the action
            document_id: ID of document involved
            target_user_id: ID of target user (for user management actions)
            level: Log level
            details: Additional details dictionary
            success: Whether the action was successful
            error_message: Error message if action failed
            ip_address: Client IP address
            user_agent: Client user agent
            session_id: Session identifier
            
        Returns:
            Created AuditLog instance or None if audit disabled
        """
        if not self.audit_config['enabled']:
            return None
        
        try:
            # Create database audit log
            audit_log = AuditLog.log_action(
                action=action,
                description=description,
                user_id=user_id,
                document_id=document_id,
                target_user_id=target_user_id,
                level=level,
                details=details,
                success=success,
                error_message=error_message,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            # Also log to file
            log_level = getattr(logging, level.value.upper())
            log_message = self._format_log_message(
                action, description, user_id, document_id, success, error_message
            )
            self.logger.log(log_level, log_message)
            
            return audit_log
            
        except Exception as e:
            # Fallback to basic logging if audit log creation fails
            self.logger.error(f"Failed to create audit log: {e}")
            return None
    
    def _format_log_message(self, action: AuditAction, description: str,
                           user_id: Optional[int], document_id: Optional[int],
                           success: bool, error_message: Optional[str]) -> str:
        """Format log message for file logging."""
        parts = [
            f"ACTION={action.value}",
            f"DESC='{description}'",
            f"SUCCESS={success}"
        ]
        
        if user_id:
            parts.append(f"USER_ID={user_id}")
        
        if document_id:
            parts.append(f"DOC_ID={document_id}")
        
        if error_message:
            parts.append(f"ERROR='{error_message}'")
        
        return " | ".join(parts)
    
    def log_login_attempt(self, username: str, success: bool, user_id: Optional[int] = None,
                         ip_address: Optional[str] = None, user_agent: Optional[str] = None,
                         error_message: Optional[str] = None) -> Optional[AuditLog]:
        """Log user login attempt."""
        action = AuditAction.LOGIN if success else AuditAction.LOGIN_FAILED
        level = AuditLevel.INFO if success else AuditLevel.WARNING
        description = f"Login attempt for '{username}': {'SUCCESS' if success else 'FAILED'}"
        
        return self.log_action(
            action=action,
            description=description,
            user_id=user_id,
            level=level,
            success=success,
            error_message=error_message,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def log_document_access(self, action: AuditAction, document_id: int, user_id: int,
                           document_title: str, success: bool = True,
                           error_message: Optional[str] = None) -> Optional[AuditLog]:
        """Log document access action."""
        action_name = action.value.replace('_', ' ').title()
        description = f"{action_name}: '{document_title}'"
        
        return self.log_action(
            action=action,
            description=description,
            user_id=user_id,
            document_id=document_id,
            success=success,
            error_message=error_message
        )
    
    def log_user_management(self, action: AuditAction, admin_user_id: int,
                           target_user_id: int, target_username: str,
                           details: Optional[Dict[str, Any]] = None) -> Optional[AuditLog]:
        """Log user management action."""
        action_name = action.value.replace('_', ' ').title()
        description = f"{action_name}: '{target_username}'"
        
        return self.log_action(
            action=action,
            description=description,
            user_id=admin_user_id,
            target_user_id=target_user_id,
            details=details
        )
    
    def log_system_event(self, action: AuditAction, description: str,
                        details: Optional[Dict[str, Any]] = None,
                        level: AuditLevel = AuditLevel.INFO,
                        success: bool = True,
                        error_message: Optional[str] = None) -> Optional[AuditLog]:
        """Log system event."""
        return self.log_action(
            action=action,
            description=description,
            details=details,
            level=level,
            success=success,
            error_message=error_message
        )
    
    def log_security_event(self, description: str, user_id: Optional[int] = None,
                          ip_address: Optional[str] = None,
                          details: Optional[Dict[str, Any]] = None) -> Optional[AuditLog]:
        """Log security-related event."""
        return self.log_action(
            action=AuditAction.SYSTEM_STARTUP,  # Generic security action
            description=f"SECURITY: {description}",
            user_id=user_id,
            level=AuditLevel.WARNING,
            details=details,
            ip_address=ip_address
        )
    
    def get_user_activity(self, user_id: int, days: int = 30,
                         limit: int = 100) -> List[AuditLog]:
        """
        Get recent activity for a user.
        
        Args:
            user_id: User ID
            days: Number of days to look back
            limit: Maximum number of records
            
        Returns:
            List of audit log entries
        """
        return AuditLog.get_user_activity(user_id, limit=limit)
    
    def get_document_activity(self, document_id: int, days: int = 30,
                             limit: int = 100) -> List[AuditLog]:
        """
        Get recent activity for a document.
        
        Args:
            document_id: Document ID
            days: Number of days to look back
            limit: Maximum number of records
            
        Returns:
            List of audit log entries
        """
        return AuditLog.get_document_activity(document_id, limit=limit)
    
    def get_recent_activity(self, hours: int = 24, limit: int = 100,
                           level: Optional[AuditLevel] = None) -> List[AuditLog]:
        """
        Get recent system activity.
        
        Args:
            hours: Number of hours to look back
            limit: Maximum number of records
            level: Filter by log level
            
        Returns:
            List of audit log entries
        """
        return AuditLog.get_recent_activity(limit=limit, level=level)
    
    def get_failed_actions(self, hours: int = 24, limit: int = 100) -> List[AuditLog]:
        """
        Get recent failed actions.
        
        Args:
            hours: Number of hours to look back
            limit: Maximum number of records
            
        Returns:
            List of failed audit log entries
        """
        return AuditLog.get_failed_actions(hours=hours, limit=limit)
    
    def get_security_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get security alerts based on audit logs.
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            List of security alert dictionaries
        """
        alerts = []
        
        try:
            # Get failed login attempts
            failed_logins = AuditLog.get_failed_actions(hours=hours)
            login_failures = [log for log in failed_logins 
                            if log.action == AuditAction.LOGIN_FAILED.value]
            
            if len(login_failures) > 5:  # Threshold for alert
                alerts.append({
                    'type': 'multiple_failed_logins',
                    'severity': 'high',
                    'count': len(login_failures),
                    'description': f'{len(login_failures)} failed login attempts in the last {hours} hours',
                    'timestamp': datetime.utcnow()
                })
            
            # Check for unusual activity patterns
            recent_activity = self.get_recent_activity(hours=hours, limit=1000)
            
            # Group by user
            user_activity = {}
            for log in recent_activity:
                if log.user_id:
                    if log.user_id not in user_activity:
                        user_activity[log.user_id] = []
                    user_activity[log.user_id].append(log)
            
            # Check for users with excessive activity
            for user_id, activities in user_activity.items():
                if len(activities) > 100:  # Threshold for excessive activity
                    alerts.append({
                        'type': 'excessive_user_activity',
                        'severity': 'medium',
                        'user_id': user_id,
                        'count': len(activities),
                        'description': f'User {user_id} performed {len(activities)} actions in {hours} hours',
                        'timestamp': datetime.utcnow()
                    })
            
        except Exception as e:
            self.logger.error(f"Error generating security alerts: {e}")
        
        return alerts
    
    def cleanup_old_logs(self, days: int = 90) -> int:
        """
        Clean up old audit logs.
        
        Args:
            days: Number of days to keep logs
            
        Returns:
            Number of deleted records
        """
        try:
            deleted_count = AuditLog.cleanup_old_logs(days=days)
            
            self.log_system_event(
                action=AuditAction.SYSTEM_STARTUP,  # Generic maintenance action
                description=f"Audit log cleanup: {deleted_count} old records removed",
                details={'days_kept': days, 'records_deleted': deleted_count}
            )
            
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Audit log cleanup failed: {e}")
            return 0
    
    def generate_activity_report(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Generate activity report for a date range.
        
        Args:
            start_date: Start date for report
            end_date: End date for report
            
        Returns:
            Dictionary with activity statistics
        """
        from app.db.session import get_db_session
        from sqlalchemy import func
        
        report = {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'total_actions': 0,
            'successful_actions': 0,
            'failed_actions': 0,
            'actions_by_type': {},
            'actions_by_user': {},
            'actions_by_day': {},
            'top_documents': []
        }
        
        try:
            with get_db_session() as session:
                # Get all logs in date range
                logs = session.query(AuditLog).filter(
                    AuditLog.created_at >= start_date,
                    AuditLog.created_at <= end_date
                ).all()
                
                report['total_actions'] = len(logs)
                
                # Analyze logs
                for log in logs:
                    # Count success/failure
                    if log.success == 'true':
                        report['successful_actions'] += 1
                    else:
                        report['failed_actions'] += 1
                    
                    # Count by action type
                    action = log.action
                    if action not in report['actions_by_type']:
                        report['actions_by_type'][action] = 0
                    report['actions_by_type'][action] += 1
                    
                    # Count by user
                    if log.user_id:
                        user_id = str(log.user_id)
                        if user_id not in report['actions_by_user']:
                            report['actions_by_user'][user_id] = 0
                        report['actions_by_user'][user_id] += 1
                    
                    # Count by day
                    day = log.created_at.date().isoformat()
                    if day not in report['actions_by_day']:
                        report['actions_by_day'][day] = 0
                    report['actions_by_day'][day] += 1
                
                # Get top documents
                document_counts = {}
                for log in logs:
                    if log.document_id:
                        doc_id = log.document_id
                        if doc_id not in document_counts:
                            document_counts[doc_id] = 0
                        document_counts[doc_id] += 1
                
                # Sort and get top 10
                sorted_docs = sorted(document_counts.items(), 
                                   key=lambda x: x[1], reverse=True)[:10]
                
                for doc_id, count in sorted_docs:
                    report['top_documents'].append({
                        'document_id': doc_id,
                        'action_count': count
                    })
                
        except Exception as e:
            self.logger.error(f"Error generating activity report: {e}")
        
        return report

# Global audit service instance
audit_service = AuditService()
